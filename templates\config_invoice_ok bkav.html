<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON> hình hóa đơn</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <style>
	html, body {
	  height: 100%;
	  margin: 0;
	  background: #f8f9fa;
	  font-family: 'Arial', sans-serif;
	  overflow: auto;
	}

	.setting-container {
	  max-width: 100%;
	  height: 100vh;
	  margin: 0 auto;
	  padding: 30px 40px;
	  background: #f8f9fa;
	  border-radius: 16px;
	  display: flex;
	  flex-direction: column;
	}


    label {
      font-weight: bold;
    }

    h3.title {
      color: #dc3545;
      font-weight: bold;
      text-align: center;
      margin-bottom: 1px;
	  margin-top: 1px;
    }
	button.btn {
	  width: 22%;
	  height: 52px;
	  font-size: 18px;
	  background-color: #063970;
	  color: white;
	  border: none;
	  border-radius: 8px;
	}

	/* Styling cho BKAV validation */
	.text-danger {
	  color: #dc3545 !important;
	}

	.form-control:invalid, .form-select:invalid {
	  border-color: #dc3545;
	  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
	}

	.form-control:valid, .form-select:valid {
	  border-color: #198754;
	}

	/* Tooltip cho cmd_type options */
	select#cmd_type option {
	  padding: 8px;
	  font-size: 14px;
	}

	/* Highlight cho required fields */
	label .text-danger {
	  font-weight: bold;
	}
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body>
  <div class="setting-container">
    <h3 class="title">CẤU HÌNH ĐƠN VỊ HÓA ĐƠN</h3>
	<hr class="my-1" style="border-top: 3px solid #0d6efd;" />
    <div class="mb-2">
      <label for="provider">Chọn nhà cung cấp:</label>
      <select id="provider" class="form-select" onchange="loadProviderFields()">
        <option value="VNPT">VNPT</option>
        <option value="MOBIFONE">MOBIFONE</option>
        <option value="VIETTEL">VIETTEL</option>
		<option value="MISA">MISA</option>
		<option value="EASYINVOICE">EASYINVOICE</option>
	    <option value="BKAV">BKAV</option>
		<option value="FAST">FAST</option>
		<option value="HILO">HILO</option>
		<option value="MOBIFONE_TEST">MOBIFONE_TEST</option>
      </select>
    </div>

    <div id="bkavHelperText" class="alert alert-info mt-3" style="display: none;">
      <h6><i class="bi bi-info-circle"></i> Hướng dẫn cấu hình BKAV:</h6>
      <ul class="mb-0">
        <li><strong>Partner GUID:</strong> Mã định danh được Bkav cung cấp (bắt buộc)</li>
        <li><strong>Partner Token:</strong> Mã Token được Bkav cung cấp (bắt buộc)</li>
        <li><strong>URL API:</strong> Đường dẫn API production của Bkav (bắt buộc)</li>
        <li><strong>URL API Test:</strong> Đường dẫn API test (tùy chọn)</li>
        <li><strong>Cmd Type:</strong> Loại lệnh tạo hóa đơn (mặc định: 101)</li>
        <li><strong>Môi trường test:</strong> Chế độ test (OFF = production, ON = test)</li>
      </ul>
    </div>

    <div id="configFields" class="row g-3"></div>

    <div class="mt-4 text-center">
      <button class="btn btn-primary px-4" onclick="saveConfig()">LƯU CẤU HÌNH</button>
    </div>
  </div>

  <script>
    const fieldDefs = {
      VNPT: [
        "Account",
		"ACpass",
		"username",
		"password",
		"link_api",
		"link_api_fkey",
        "pattern",
		"serial",
		"convert",
		"invoice_type",
		"NoDigitofPrice",
        "sellerFullName",
		"sellerName",
		"sellerTaxCode",
		"sellerAddress"
      ],
      MOBIFONE: [
        "tax_code",
		"username",
		"password",
		"template_code",
		"login_url",
		"get_cctbao_url",
		"link_api",
		"link_api_fkey",
		"pdf_url",
        "sellerFullName",
		"sellerName",
		"sellerTaxCode",
		"sellerAddress"
      ],
      MOBIFONE_TEST: [
        "tax_code",
		"username",
		"password",
		"ma_dvcs",
		"login_url",
		"get_cctbao_url",
		"link_api",
		"generate_url",
		"cctbao_id",
		"template_code",
		"sign_mode",
		"invoice_type",
		"NoDigitofPrice",
        "sellerFullName",
		"sellerName",
		"sellerTaxCode",
		"sellerAddress"
      ],
      BKAV: [
        "partnerGUID",
        "partnerToken",
        "env_testing",
        "url_api_link",
        "url_api_link_test",
        "cmd_type",
        "invoice_type_id",
        "currency_id",
        "exchange_rate",
        "no_digit_of_price",
        "sellerName",
        "sellerTaxCode",
        "sellerAddress"
      ],
    };

    async function loadProviderFields() {
      const res = await fetch('/load_config_invoice');
      const json = await res.json();
      const provider = document.getElementById("provider").value;
      const upperProvider = provider.toUpperCase();

      const container = document.getElementById("configFields");
      container.innerHTML = "";

      // Hiển thị/ẩn helper text cho BKAV
      const helperText = document.getElementById("bkavHelperText");
      if (helperText) {
        helperText.style.display = upperProvider === "BKAV" ? "block" : "none";
      }

      const fields = fieldDefs[upperProvider] || [];

      const sellerGroup = document.createElement("div");
      sellerGroup.className = "row g-4 mt-2";
      sellerGroup.innerHTML = `
		<hr class="my-1" style="border-top: 3px solid #0d6efd;" />
        <h3 class=\"title\">THÔNG TIN NGƯỜI BÁN</h3>
      `;

      for (let key of fields) {
        const isSellerField = key.toLowerCase().includes("seller");
        const formGroup = document.createElement("div");
        formGroup.className = "col-md-6";

        let inputHtml = "";

        // Xử lý các trường đặc biệt cho BKAV
        if (upperProvider === "BKAV") {
          if (key === "env_testing") {
            inputHtml = `
              <label for="${key}">${key} <span class="text-danger">*</span></label>
              <select class="form-select" id="${key}" required>
                <option value="OFF">OFF</option>
                <option value="ON">ON</option>
              </select>
            `;
          } else if (key === "cmd_type") {
            inputHtml = `
              <label for="${key}">${key}</label>
              <select class="form-select" id="${key}">
                <option value="100">100: ký hiệu do Bkav cấp. Số HĐ = 0</option>
                <option value="101">101: ký hiệu, số hđ do Bkav cấp</option>
                <option value="110">110: ký hiệu do PMKT cấp. Số HĐ = chưa có</option>
                <option value="111">111: ký hiệu, số hóa đơn do PMKT cấp</option>
                <option value="112">112: ký hiệu do PMKT cấp, số HĐ do Bkav cấp</option>
              </select>
            `;
          } else if (["invoice_type_id", "exchange_rate", "no_digit_of_price"].includes(key)) {
            const defaultValue = key === "invoice_type_id" ? "1" :
                               key === "exchange_rate" ? "1" :
                               key === "no_digit_of_price" ? "4" : "";
            inputHtml = `
              <label for="${key}">${key}</label>
              <input type="number" class="form-control" id="${key}" placeholder="${defaultValue}">
            `;
          } else if (key === "partnerGUID") {
            inputHtml = `
              <label for="${key}">${key} <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="${key}" required placeholder="Partner GUID từ Bkav">
            `;
          } else if (key === "partnerToken") {
            inputHtml = `
              <label for="${key}">${key} <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="${key}" required placeholder="Key:IV (Base64 format)">
              <small class="form-text text-muted">Format: base64_key:base64_iv</small>
            `;
          } else if (key === "currency_id") {
            inputHtml = `
              <label for="${key}">${key}</label>
              <input type="text" class="form-control" id="${key}" placeholder="VND">
            `;
          } else if (["url_api_link", "url_api_link_test"].includes(key)) {
            const isRequired = key === "url_api_link";
            inputHtml = `
              <label for="${key}">${key} ${isRequired ? '<span class="text-danger">*</span>' : ''}</label>
              <input type="url" class="form-control" id="${key}" ${isRequired ? 'required' : ''} placeholder="https://api.bkav.com.vn/...">
            `;
          } else {
            inputHtml = `
              <label for="${key}">${key}</label>
              <input type="text" class="form-control" id="${key}">
            `;
          }
        } else {
          // Xử lý mặc định cho các provider khác
          inputHtml = `
            <label for="${key}">${key}</label>
            <input type="text" class="form-control" id="${key}">
          `;
        }

        formGroup.innerHTML = inputHtml;

        if (isSellerField) {
          sellerGroup.appendChild(formGroup);
        } else {
          container.appendChild(formGroup);
        }
      }

      if (sellerGroup.children.length > 1) {
        container.appendChild(sellerGroup);
      }

      const conf = json[upperProvider] || {};

      // Set giá trị mặc định cho BKAV nếu chưa có config
      if (upperProvider === "BKAV" && Object.keys(conf).length === 0) {
        const defaults = {
          env_testing: "OFF",
          cmd_type: "100",
          invoice_type_id: "1",
          currency_id: "VND",
          exchange_rate: "1",
          no_digit_of_price: "4"
        };

        for (let key in defaults) {
          const input = document.getElementById(key);
          if (input) input.value = defaults[key];
        }
      } else {
        // Load giá trị từ config hiện tại
        for (let key in conf) {
          const input = document.getElementById(key);
          if (input) input.value = conf[key];
        }
      }
    }

    async function saveConfig() {
	  const provider = document.getElementById("provider").value.toUpperCase();
	  const fields = fieldDefs[provider] || [];

	  // Validation cho BKAV
	  if (provider === "BKAV") {
		const partnerGUID = document.getElementById("partnerGUID")?.value?.trim();
		const partnerToken = document.getElementById("partnerToken")?.value?.trim();
		const urlApiLink = document.getElementById("url_api_link")?.value?.trim();

		if (!partnerGUID) {
		  alert("❌ Vui lòng nhập Partner GUID!");
		  document.getElementById("partnerGUID")?.focus();
		  return;
		}

		if (!partnerToken) {
		  alert("❌ Vui lòng nhập Partner Token!");
		  document.getElementById("partnerToken")?.focus();
		  return;
		}

		// Validate partnerToken format (key:iv)
		if (!partnerToken.includes(':')) {
		  alert("❌ Partner Token phải có format: base64_key:base64_iv");
		  document.getElementById("partnerToken")?.focus();
		  return;
		}

		if (!urlApiLink) {
		  alert("❌ Vui lòng nhập URL API Production!");
		  document.getElementById("url_api_link")?.focus();
		  return;
		}

		// Validate URL format
		try {
		  new URL(urlApiLink);
		} catch (e) {
		  alert("❌ URL API Production không hợp lệ!");
		  document.getElementById("url_api_link")?.focus();
		  return;
		}

		const urlApiLinkTest = document.getElementById("url_api_link_test")?.value?.trim();
		if (urlApiLinkTest) {
		  try {
			new URL(urlApiLinkTest);
		  } catch (e) {
			alert("❌ URL API Test không hợp lệ!");
			document.getElementById("url_api_link_test")?.focus();
			return;
		  }
		}
	  }

	  const payload = {
		provider: provider,
		[provider]: {}
	  };

	  for (let key of fields) {
		let value = document.getElementById(key)?.value || "";

		// Xử lý giá trị mặc định cho BKAV
		if (provider === "BKAV") {
		  if (key === "currency_id" && !value) value = "VND";
		  if (key === "exchange_rate" && !value) value = "1";
		  if (key === "invoice_type_id" && !value) value = "1";
		  if (key === "no_digit_of_price" && !value) value = "4";
		  if (key === "cmd_type" && !value) value = "100";
		  if (key === "env_testing" && !value) value = "OFF";
		}

		payload[provider][key] = value;
	  }

	  const res = await fetch('/save_config_invoice', {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(payload)
	  });

	  const result = await res.json();
	  alert(result.message);

	  // ✅ Gọi restart auto_scheduler.py giống setting.html
	  try {
		await fetch("/api/restart_scheduler");
		console.log("🔁 Đã yêu cầu restart auto_scheduler.py");
	  } catch (e) {
		console.warn("❌ Lỗi khi restart auto_scheduler.py:", e);
	  }

	  // ✅ Thông báo cho các tab khác reload lại config
	  const timestamp = Date.now().toString();
	  localStorage.setItem("configInvoiceUpdatedAt", timestamp);
	  window.dispatchEvent(new StorageEvent("storage", {
		key: "configInvoiceUpdatedAt",
		newValue: timestamp
	  }));
	}


	window.onload = async () => {
	  try {
		const res = await fetch("/load_config_invoice");
		const config = await res.json();

		const provider = config.provider?.toUpperCase() || "VNPT";
		const providerSelect = document.getElementById("provider");
		if (providerSelect) providerSelect.value = provider;

		await loadProviderFields();  // tự động load fields theo provider đang dùng
	  } catch (e) {
		console.error("❌ Không load được cấu hình hóa đơn:", e);
	  }
	};

  </script>
</body>
</html>
