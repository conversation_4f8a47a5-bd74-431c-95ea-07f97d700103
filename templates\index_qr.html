<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>MontechPOS-QR</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"/>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>window.IS_QR_ONLY = true;</script>
  <style>
    html, body {
	  height: 100%;
	  overflow: hidden; /* Không cho cuộn toàn trang */
      font-size: 15px;
      background: #f8f9fa;
	  display: flex;
	  flex-direction: column;
    }
    .container {
	  flex: 1;
	  overflow-y: auto;
	  padding-top: 0.5rem;
	  padding-bottom: 0.5rem;
	  margin-bottom: 0; /* dòng này để container không đẩy footer ra xa */
    }
	.container, .setting-container {
		max-width: 100%;
		margin: 0;
		padding: 5px; /* hoặc 0 nếu muốn sát viền tuyệt đối */
	}
    .form-label {
      margin-bottom: 2px;
      font-weight: 500;
	  display: flex;
      align-items: center;
      justify-content: center;
    }

    .form-control, .form-select {
      font-size: 14px;
      padding: 4px 8px;
    }
    .btn {
      padding-top: 6px;
      padding-bottom: 6px;
      font-size: 14px;
    }

    .summary-bar {
      background: white;
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 5px 12px;
      font-size: 13px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      row-gap: 6px;
      column-gap: 16px;
      line-height: 1;
    }
    .summary-bar div,
    .summary-bar label {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .summary-bar b {
      color: red;
    }
	.table {
		border: 1px solid #dee2e6 !important;
	}
    .table th {
      font-size: 16px;
      background: #e9ecef;
      text-align: center;
      position: sticky;
      top: 0;
      z-index: 1;
    }
    .table td {
      font-size: 13px;
      white-space: nowrap;
      vertical-align: middle;
    }
	
	.table-bordered th, .table-bordered td {
	  border: 1px solid #dee2e6 !important;
	}	
    .table-responsive {
      max-height: 70vh;
      overflow-y: auto;
    }
	#logTable th,
	#logTable td {
	  font-size: 15px !important;
	  line-height: 1.6;
	}
		.footer {
      text-align: center;
      font-size: 13px;
      color: gray;
      margin-top: 10px;
    }
	
    @media (max-width: 768px) {
    .modal-content {
      padding: 8px;
    }
	#xmlPreview .modal-content {
	margin-top: 0 !important;   /* Đẩy modal-content lên sát top */
	}

	#xmlPreview {
	  align-items: flex-start !important;  /* Đẩy cả modal ra top */
	  justify-content: center;
	  padding-top: 10px; /* nếu muốn hơi cách nhẹ 10px cho đẹp */
	}
    #previewContent {
      font-size: 12px;
      padding: 8px 0px;
	  min-height: 650px;
    }
    .preview-body {
      padding: 4px;
    }
	.invoice-form input,
	.invoice-form select {
	  margin-bottom: 0px !important; /* Siêu gọn */
	}
	.invoice-form .d-flex,
	#xmlFormPopup .modal-content > div {
	  background: none !important; /* Xóa mọi màu nền dư */
	}
	.invoice-form > div {
	  margin-bottom: 0px !important; /* Siêu gọn giữa các cụm label+input */
	}
    .invoice-table th, .invoice-table td {
      font-size: 12px;
      padding: 4px;
      word-break: break-word;
    }
	
	.modal-content {
	  overflow-y: auto;
	  max-height: 90vh; /* hoặc 95vh */
	}
	.footer {
	  text-align: center;
	  font-size: 12px;
	  color: #444;
	  padding: 2px 0; /* gọn gàng */
	  margin: 0;       /* bỏ luôn margin mặc định nếu có */
	  background: transparent; /* tùy bạn có thể có hoặc không */
	  flex-shrink: 0;  /* giữ footer cố định dưới */
	}

  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });


</script>
</head>
<body>
<div class="container py-2">
  <!-- Header -->
	<div class="d-flex flex-column align-items-center gap-2 mb-2 text-center">
	  <div>
		<h5 id="sellerName" style="font-size: 16px; font-weight: bold; color: red; margin: 0;">CÔNG TY MONTECH</h5>
			<p class="m-0" style="font-size: 12px; font-weight: bold; color: #063970; white-space: nowrap;">
			  <span id="sellerTaxcode">**********</span>
			  <span id="sellerFullName">Montech</span>
			  <span id="providerLabel" class="text-muted ms-1" style="font-weight: normal;"></span>
			</p>
	  </div>
	</div>

	<div class="d-flex justify-content-between align-items-center mb-2">
	  <!-- Trái: fetchStatus -->
	  <div id="fetchStatus" style="font-size: 13px; color: gray; text-align: center;"></div>
	  <div id="activeUserCount" style="font-size: 13px; color: gray; text-align: center;"></div>

	  <!-- Phải: Cài đặt + Tài khoản -->
	  <div class="d-flex align-items-center gap-2">
		<!-- Nút cài đặt có dropdown mở tab mới -->
		{% set username = session.get('username') %}
		{% set role = session.get('role') %}

		{% if role == 'admin' %}
		  <div class="dropdown">
			<button class="btn btn-sm dropdown-toggle btn-setting" data-bs-toggle="dropdown" title="Cài đặt hệ thống">
			  <i class="fa-solid fa-gear"></i> Cài đặt
			</button>
			<ul class="dropdown-menu dropdown-menu-end">
			  <li><a class="dropdown-item" href="/check_invoice" target="_blank">📊 Quản lý xuất hóa đơn</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/customer" target="_blank">🧑‍💼 Quản lý khách hàng</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/setting" target="_blank">🛠️ Cài đặt xuất tự động</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/config_invoice" target="_blank">🧬 Cấu hình hóa đơn</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/bank_setting" target="_blank">💳 Thanh toán QR</a></li>

			  {% if username == 'montech' %}
				<li><hr class="dropdown-divider"></li>
				<li><a class="dropdown-item" href="/check_log" target="_blank">🔐 Check giao dịch</a></li>
				<li><hr class="dropdown-divider"></li>
				<li><a class="dropdown-item" href="/debug" target="_blank">🗂 ️Check debug</a></li>
			  {% endif %}
			</ul>
		  </div>
		{% endif %}

		<!-- Icon user và tên -->
		<div class="dropdown">
		  <button class="btn btn-sm dropdown-toggle btn-setting d-flex align-items-center gap-2" data-bs-toggle="dropdown" title="Tài khoản">
			<i class="fa-solid fa-user"></i>
			<span>{{ session.get('username') }}</span>
		  </button>
		  <ul class="dropdown-menu dropdown-menu-end">
			{% if role == 'admin' %}
			  <li><a class="dropdown-item" href="/user_manage" target="_blank">🕵️‍♂️ Quản lý tài khoản</a></li>
			{% endif %}
			<li><hr class="dropdown-divider"></li>
			<li><a class="dropdown-item text-danger" href="/logout">👋 Đăng xuất</a></li>
		  </ul>
		</div>
	  
	  </div>
	  
	</div>



  <!-- Bộ lọc -->
  <div class="row g-2 mb-2 text-center">
    <div class="col-3"><label class="form-label text-center d-block w-100">Ngày</label><input type="date" id="filterDate" class="form-control" /></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Từ giờ</label><input type="time" id="filterStartTime" class="form-control" /></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Đến giờ</label><input type="time" id="filterEndTime" class="form-control" /></div>
	<div class="col-3"><label class="form-label text-center d-block w-100">Tìm nhanh</label><input id="filterCode" class="form-control" placeholder="Tiền, Mã GD..." /></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Cò bơm</label><select id="filterFaucet" class="form-select"></select></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Nhiên liệu</label><select id="filterFuel" class="form-select"></select></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Đơn giá</label><select id="filterPrice" class="form-select"></select></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Bồn</label><select id="filterTank" class="form-select"></select></div>	
  </div>

  <!-- Nút chức năng -->

  <!-- Thống kê -->
  <div id="logSummary" class="summary-bar"></div>

  <!-- Bảng log index_qr -->
  <div class="table-responsive mb-3">
    <table id="logTable" class="table table-bordered table-hover table-sm">
      <thead>
        <tr>
		  <th>CÒ</th>
		  <th class="sortable" onclick="sortTable(this)">TIỀN<i class="fa-solid fa-sort"></i></th>
          <th class="sortable" onclick="sortTable(this)">LÍT<i class="fa-solid fa-sort"></i></th>
		  <th>GIÁ</th>
          <th>QR</th>
		  <th>NHIÊN LIỆU</th>
          <th class="sortable" onclick="sortTable(this)">NGÀY<i class="fa-solid fa-sort"></i></th>
          <th class="sortable" onclick="sortTable(this)">GIỜ<i class="fa-solid fa-sort"></i></th>
		  <th>SERI</th>  
        </tr>
      </thead>
      <tbody id="logBody"></tbody>
    </table>
  </div>
  
  <!-- Phân trang 
	<div class="pagination-controls d-flex align-items-center justify-content-between mt-2" style="gap: 14px; flex-wrap: wrap; user-select: none;">-->
	<div class="pagination-controls d-none">
	  <select id="logsPerPageSelect" onchange="changeLogsPerPage()" class="form-select" style="width: 80px;">
		<option value="100">100</option>
		<option value="200">200</option>
		<option value="300">300</option>
		<option value="500">500</option>
		<option value="8000" selected>Tất cả</option>
	  </select>
	  
	  <div id="paginationInfo" style="font-size: 14px; color: #444;"></div>
	  
	  <div class="pagination-buttons d-flex align-items-center" style="gap: 4px;">
		<button onclick="goToFirstPage()">«</button>
		<button onclick="goToPrevPage()">‹</button>
		<span id="paginationNumbers"></span>
		<button onclick="goToNextPage()">›</button>
		<button onclick="goToLastPage()">»</button>
	  </div>
	</div> 
	

</div>

<div class="footer" style="text-align: center; font-size: 12px; color: #444;">© Montech - 2025</div>	


<script>
  function showModal(id) {
    document.getElementById(id)?.classList.add("show");
  }
  function hideModal(id) {
    document.getElementById(id)?.classList.remove("show");
  }
  function closePreview() {
    document.getElementById("xmlPreview")?.classList.remove("show");
    window.previewData = null;
    loadDefaultCustomerData();
    document.getElementById("previewContent").innerHTML = "";
  }
  // Gán global
  window.showModal = showModal;
  window.hideModal = hideModal;
  window.closePreview = closePreview;
</script>


<script>
  // Chỉ reload nếu KHÔNG phải trang QR
  const isQROnly = window.IS_QR_ONLY === true;

  window.addEventListener("storage", function(e) {
    if (!isQROnly) return;

    const key = e.key;
    const changes = {
      settingsUpdatedAt: "✅ Đã cập nhật cài đặt",
      configInvoiceUpdatedAt: "✅ Đã cập nhật cấu hình hóa đơn",
      bankSettingUpdatedAt: "✅ Đã cập nhật tài khoản ngân hàng"
    };

    if (changes[key]) {
      console.log("🔄 " + changes[key]);

      Swal.fire({
        icon: "info",
        title: changes[key],
        text: "Thông tin đã được áp dụng.",
        timer: 2000,
        showConfirmButton: false,
        willClose: () => {
          location.reload();
        }
      });
    }
  });

//==============================================================================
let currentFilteredLogs = [];
let userIsInteracting = false;
let interactionTimeout = null;
let currentPage = 1;
const perPage = 100;
let allLogs = [];              // Chứa toàn bộ log
let logsPerPage = parseInt(document.getElementById("logsPerPageSelect")?.value) || 100;

window.fuelMap = {};

fetch("/load_config_invoice")
  .then(res => res.json())
  .then(config => {
    window.currentConfig = config;

    const provider = config.provider?.toLowerCase() || "vnpt";

    // Cập nhật dòng providerLabel
    const labelEl = document.getElementById("providerLabel");
    if (labelEl) labelEl.textContent = `(${provider.toUpperCase()})`;

    // Cập nhật trạng thái auto gửi
    //updateAutoConfigStatus();

    // Cập nhật tên công ty + MST
    const conf = config[provider.toUpperCase()] || {};
    const nameEl = document.getElementById("sellerName");
    const taxEl = document.getElementById("sellerTaxcode");
    const FullNameEl = document.getElementById("sellerFullName");	

    if (nameEl) nameEl.textContent = conf.sellerName || "";
    if (taxEl) taxEl.textContent = `MST: ${conf.sellerTaxCode || ""} - `;
	if (FullNameEl) FullNameEl.textContent = conf.sellerFullName || "";
  })
  .catch(err => {
    console.error("❌ Lỗi load config:", err);
  });

// ------------------------------------------------------------------------
/*Tải log từ MonBox khi F5 hoặc mở trang
function importLogFromMonboxOnce() {
  const now = Date.now();
  const lastCall = localStorage.getItem("lastImportLogTime");

  if (lastCall && now - parseInt(lastCall) < 30_000) {
    console.log("⏳ Chặn gọi import log vì F5 liên tục (cách chưa đủ 30s)");
    return;
  }

  localStorage.setItem("lastImportLogTime", now);

	fetch("/api/import_log", { method: "POST" })
	  .then(async res => {
		if (!res.ok) {
		  throw new Error(`Máy chủ trả về lỗi ${res.status}`);
		}
		return res.json();
	  })
	  .then(data => {
		console.log("📥 [F5] Tải log MonBox:", data);
		if (!data.success) {
		  Swal.fire({
			icon: "error",
			html: `
			  <div style="font-size: 15px;">
				Lỗi: <b>${data.message || "Không rõ nguyên nhân"}</b><br>
				Vui lòng kiểm tra thiết bị MonBox.
			  </div>
			`,
			timer: 30000
		  });
		}
	  })
	  .catch(err => {
		console.error("❌ Lỗi tải log:", err);
		Swal.fire({
		  icon: "error",
		  html: `
			<div style="font-size: 16px;">
			  Không thể truy cập thiết bị MonBox.<br><br>
			  <b>Vui lòng thực hiện:</b>
			  <ul style="text-align:left; margin-top:5px;">
				<li>Tắt modem mạng và thiết bị MonBox</li>
				<li>Chờ 5 phút → Bật modem, rồi bật MonBox</li>
				<li>Đợi khoảng 3 phút → Tải lại trang (F5)</li>
			  </ul>
			</div>
		  `,
		  confirmButtonText: "OK",
		  confirmButtonColor: "#6c63ff",
		  width: 440,
		  timer: 30000
		});
	  });
} */

// ------------------------------------------------------------------------
function markUserActive() {
  userIsInteracting = true;
  clearTimeout(interactionTimeout);
  interactionTimeout = setTimeout(() => {
    userIsInteracting = false;
    console.log("✅ Không thao tác → cho phép fetchLogs()");
  }, 8000); // reset sau 8 giây
}

// Gắn tất cả sự kiện tương tác
["click", "keydown", "wheel", "scroll", "input", "focusin"].forEach(evt => {
  document.addEventListener(evt, markUserActive, true);
});

let currentSort = { column: null, asc: true };
let autoSendActive = false;
let autoSendInterval = null;
let latestLogCode = "";  // Mã giao dịch mới nhất để so sánh log mới
let lastFetchTime = 0;   // Lưu thời điểm lần fetchLogs() gần nhất


function getLocalDateString() {
  const now = new Date();
  now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
  return now.toISOString().split('T')[0];
}

// ===== Khi chọn Từ ngày Đến ngày chỉ cho phép =<30 ngày ====================
async function validateDateRange(showPopup = true) {
  const startInput = document.getElementById('startDate');
  const endInput = document.getElementById('endDate');

  const start = new Date(startInput.value);
  const end = new Date(endInput.value);

  if (!start || !end || isNaN(start) || isNaN(end)) {
    return false; // thiếu ngày thì không làm gì
  }

  const diffMs = end.getTime() - start.getTime();
  const diffDays = diffMs / (1000 * 60 * 60 * 24);

  console.log(`🔍 Bạn đã chọn khoảng: ${diffDays.toFixed(0)} ngày.`);

  if (diffDays > 92) { // tối đa 1 quý
    await Swal.fire({
      icon: 'warning',
      title: 'Khoảng thời gian quá dài!',
      html: `Thời gian chọn là <b>${diffDays.toFixed(0)}</b> ngày. Bấm <b>OK</b> để chọn lại tối đa 3 tháng.`,
      confirmButtonText: 'OK'
    });
    return false;
  } 
  
  // Nếu số ngày trong khoảng từ 1 đến 30 mới hiện popup
	if (diffDays > 0 && showPopup) {
	  await Swal.fire({
		icon: 'success',
		title: '',
		html: `Thời gian chọn là <b>${diffDays.toFixed(0)}</b> ngày. Bấm <b>OK</b> để tải giao dịch.`,
		confirmButtonText: 'OK'
	  });
	}

  return true;
}


window.addEventListener("DOMContentLoaded", () => {
  // Gán ngày mặc định
  const today = new Date(getLocalDateString());
  document.getElementById('filterDate').value = today.toISOString().slice(0, 10);

  // Gán sự kiện lọc
  document.getElementById("filterDate").onchange = fetchLogs;
  document.getElementById("filterStartTime").onchange = fetchLogs;
  document.getElementById("filterEndTime").onchange = fetchLogs;
  document.getElementById("filterFaucet").onchange = fetchLogs;
  document.getElementById("filterFuel").onchange = fetchLogs;
  document.getElementById("filterPrice").onchange = fetchLogs;
  document.getElementById("filterTank").onchange = fetchLogs;
  document.getElementById("filterCode").oninput = fetchLogs;

  // Gọi các hàm chính
  loadFuelMap();
  loadTankMap();
  //importLogFromMonboxOnce();  // Tải log từ MonBox khi F5 hoặc mở trang
  fetchLogs();
  //loadDefaultCustomerData();
  //updateAutoConfigStatus();
  
  // Tự động gọi fetchLogs mỗi 10 giây, nhưng chỉ nếu người dùng không thao tác
  setInterval(() => {
	  (async () => {
		const popupVisible = document.getElementById("xmlPreview")?.classList.contains("show");
		const filterInvoiceOnly = document.getElementById("filterHasInvoice")?.checked;
		const filterInternalOnly = document.getElementById("filterInternalOnly")?.checked;
		const filterQRPaidOnly = document.getElementById("filterQRPaidOnly")?.checked;

		console.log("🌀 AutoCheck:", {
		  autoSendActive,
		  userIsInteracting,
		  popupVisible,
		  filterInvoiceOnly
		});

		//*if (autoSendActive) {
		//  updateFetchStatus("⏸️ Auto ON");
		//  return;
		//}
		
		if (window.isBatchSending) {
		  updateFetchStatus("⏸️ Đang gởi hóa đơn");
		  return;
		}


		if (userIsInteracting || popupVisible || filterInvoiceOnly || filterInternalOnly) {
		  updateFetchStatus("⏸️ Đang thao tác");
		  return;
		}

		try {
		  const res = await fetch("/api/latest_log_code");
		  const data = await res.json();
		  const now = Date.now();
		  if (data.code && data.code !== latestLogCode && now - lastFetchTime > 3000) {
			  console.log("🆕 Có GD mới:", data.code);
			  fetchLogs();
		  }

		} catch (e) {
		  console.warn("❌ Lỗi khi kiểm tra GD mới:", e);
		}
	  })();
   }, 15000); // Gộp thành 1 lần kiểm tra mỗi 15 giây
});


function formatLit(value) {
  return (value / 1000).toLocaleString('vi-VN', { minimumFractionDigits: 3, maximumFractionDigits: 3 });
}

// Hàm gọi API để load fuel.json từ backend
async function loadFuelMap() {
  try {
    const res = await fetch("/setting/fuel"); // gọi API Flask
    const data = await res.json(); // đọc JSON trả về
    window.fuelMap = data; // gán vào biến toàn cục
    console.log("✅ Đã load fuelMap:", data);
  } catch (e) {
    console.error("❌ Không load được fuelMap:", e);
    window.fuelMap = {};
  }
}
// Hàm gọi API để load tank.json từ backend
async function loadTankMap() {
  try {
    const res = await fetch("/tank_map"); // sử dụng endpoint cho tank (đã định nghĩa trong app.py)
    const data = await res.json();
    window.tankMap = data; // gán vào biến toàn cục
    console.log("✅ Đã load tankMap:", data);
  } catch (e) {
    console.error("❌ Không load được tankMap:", e);
    window.tankMap = {};
  }
}


function getMappedFuelName(code) {
  return window.fuelMap?.[code] || code;
}
window.getMappedFuelName = getMappedFuelName; // để toàn hệ thống dùng được

function getMappedTankName(serial) {
  return (window.tankMap?.[serial] || "").trim();
}
window.getMappedTankName = getMappedTankName; // để toàn hệ thống dùng được


// ======= HÀM fetchLogs() CẬP NHẬT VÀ LOAD LẠI ===========================
async function fetchLogs() {

  updateFetchStatus("🟢 Đang tải GD...");

  const selectedDate = document.getElementById("filterDate").value;

  const res = await fetch(`/logs?start=${selectedDate}&end=${selectedDate}`);


  const filterInvoiceOnly = document.getElementById("filterHasInvoice")?.checked;
  const filterInternalOnly = document.getElementById("filterInternalOnly")?.checked;
  const filterQRPaidOnly = document.getElementById("filterQRPaidOnly")?.checked;

  // Cập nhật trạng thái theo checkbox
  if (filterInvoiceOnly) {
	  updateFetchStatus("⏸️ Đang thao tác");
  } else {
	  updateFetchStatus("🟢 Đang tải GD...");
	}
  
  const data = await res.json();
  if (data.logs?.length) {
	latestLogCode = data.logs[0].transactionCode || latestLogCode;
  }
  lastFetchTime = Date.now();  // Cập nhật mốc thời gian fetch

  const startTime = document.getElementById("filterStartTime")?.value;
  const endTime = document.getElementById("filterEndTime")?.value;
  const selectedFuel = document.getElementById("filterFuel").value;
  const selectedFaucet = document.getElementById("filterFaucet").value;
  const selectedPrice = document.getElementById("filterPrice").value;
  const selectedTank = document.getElementById("filterTank").value;  
  const searchCode = document.getElementById("filterCode").value.trim().toLowerCase();

  const fuels = new Set();
  const faucets = new Set();
  const prices = new Set();
  const tanks = new Set();

  const dateFiltered = data.logs.filter(log => {
  const matchDate = !selectedDate || log.transactionDate === selectedDate;					  
  const matchTime = (!startTime || log.transactionTime >= startTime) &&(!endTime || log.transactionTime <= endTime);
  return matchDate && matchTime;
  });

	fuels.clear();
	faucets.clear();
	prices.clear();
	tanks.clear();
	
	for (let log of dateFiltered) {
	  const tankName = getMappedTankName(log.pumpSerial);
	  const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
	  const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
	  const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;
	  const matchTank = !selectedTank || tankName === selectedTank;

	  if (matchFaucet && matchPrice && matchTank) fuels.add(log.fuelType);
	  if (matchFuel && matchPrice && matchTank) faucets.add(log.faucetNo);
	  if (matchFuel && matchFaucet && matchTank) prices.add(log.transactionPrice);
	  if (matchFuel && matchFaucet && matchPrice) tanks.add(tankName);
	}

  fillSelect("filterFaucet", faucets);
  fillSelect("filterFuel", fuels);
  fillSelect("filterPrice", prices);
  fillSelect("filterTank", tanks); 
	
  const filtered = dateFiltered.filter(log => {
    const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
    const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
    const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;
	const matchCode =!searchCode ||log.transactionCode.toLowerCase().includes(searchCode) ||log.transactionCost.toString().includes(searchCode);
	const matchQRPaid = !filterQRPaidOnly || log.qr_paid === true;
	const matchTank = !selectedTank || getMappedTankName(log.pumpSerial) === selectedTank;
	
	return matchFuel && matchFaucet && matchPrice && matchTank && matchCode && matchQRPaid;
  }); 

	// Sắp xếp log đã lọc
	filtered.sort((a, b) => {
	  if (a.transactionDate !== b.transactionDate)
		return b.transactionDate.localeCompare(a.transactionDate);
	  return b.transactionTime.localeCompare(a.transactionTime);
	});

	// Gán và hiển thị
  allLogs = data.logs;
  currentFilteredLogs = filtered;

  renderPagination();
  renderTableByPage();
  renderSummary(currentFilteredLogs);

  updateFetchStatus("✅ Cập nhật " + new Date().toLocaleTimeString('vi-VN', { hour12: false }));

}


function fillSelect(id, values) {
  const select = document.getElementById(id);
  const current = select.value;
  select.innerHTML = '<option value="">Tất cả</option>';

  [...values]
    .sort((a, b) => Number(a) - Number(b))  // 👉 đảm bảo đúng thứ tự số
    .forEach(val => {
      const label =
        id === "filterFuel" ? (window.fuelMap?.[val] || val) :
        id === "filterTank" ? val :  // hoặc custom thêm nếu muốn gắn tên bồn
        val;

      select.innerHTML += `<option value="${val}">${label}</option>`;
    });

  select.value = current;
}


function renderTable(logs) {
  const tbody = document.getElementById('logBody');
  tbody.innerHTML = '';
  const isMobile = window.innerWidth < 768;
  const isQROnly = window.IS_QR_ONLY === true;
 
  for (let log of logs) {
    const row = document.createElement('tr');

    // Thêm xử lý màu QR dựa vào log.qr_paid
    const qrColor = log.qr_paid ? "text-success" : "text-danger";

	if (isQROnly) {
	  actionsHtml = `
		<button onclick="generateQR('${log.transactionCode}')" title="Tạo mã QR">
		  <i class="fa-solid fa-qrcode ${qrColor}"></i>
		</button>`;
	} else {
	  actionsHtml = `
		<button onclick="${actionOnClick}" title="Xem chi tiết">
		  <i class="fa fa-eye ${eyeColor}"></i>
		</button>
		<button onclick="generateQR('${log.transactionCode}')" title="Tạo mã QR" style="margin-left: 4px;">
		  <i id="qr-icon-${log.transactionCode}" class="fa-solid fa-qrcode ${qrColor}"></i>
		</button>`;
	}
	
	if (isQROnly) {
	  row.innerHTML = `
        <td>${log.faucetNo}</td>
		<td>${log.transactionCost.toLocaleString('vi-VN')}</td>
        <td>${formatLit(log.transactionAmount)}</td>
        <td>${log.transactionPrice.toLocaleString('vi-VN')}</td>
        <td>${actionsHtml}</td>   
        <td>${getMappedFuelName(log.fuelType)}</td>		
		<td>${log.transactionDate.split("-").reverse().join("-")}</td>
        <td>${log.transactionTime}</td>
        <td>${log.pumpSerial}</td>
      `;
	tbody.appendChild(row);
	}
  }
}


function renderSummary(logs) {
  const isMobile = /Android|iPhone|iPod|Mobile|Windows Phone/i.test(navigator.userAgent);


  let totalAmount = 0;
  let totalVolume = 0;

  for (let log of logs) {
    totalAmount += log.transactionCost;
    totalVolume += log.transactionAmount / 1000;
  }
  
  const countPaidQR = logs.filter(log => log.qr_paid).length;

  const wasQRPaidChecked = document.getElementById("filterQRPaidOnly")?.checked;
  
  

  let summaryHTML = '';

  if (isQROnly) {
	// Mobile
	summaryHTML = `
	  <div class="d-grid gap-1 small text-center" style="line-height: 1.6;">
		<div style="white-space: nowrap; font-size: 12px;">
		  Tổng tiền: <b class="stat-number text-primary">${totalAmount.toLocaleString('vi-VN')}</b>&nbsp;|
		  Tổng Lít: <b class="stat-number text-primary">${totalVolume.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</b>&nbsp;|
		  Tổng GD: <b class="stat-number text-primary">${logs.length}</b>
		</div>
		<div style="white-space: nowrap; font-size: 13px; font-weight: bold;">
		  <input type="checkbox" id="filterQRPaidOnly" onchange="onToggleQRPaid()" />
		  Đã chuyển khoản<b class="stat-number text-success">${countPaidQR}</b> giao dịch
		</div>
		<div id="autoSummary">
	  </div>
	`;
  }

  document.getElementById("logSummary").innerHTML = summaryHTML;
  if (wasQRPaidChecked) document.getElementById("filterQRPaidOnly").checked = true;
}

// ==== Xử lý tick vào ô lọc Đã CK QR ===========
function onToggleQRPaid() {
  const qrCheckbox = document.getElementById("filterQRPaidOnly");
  fetchLogs(); // load lại log có lọc
}


function sortTable(thElement) {
	const table = thElement.closest("table");
	const tbody = table.querySelector("tbody");
	const rows = Array.from(tbody.querySelectorAll("tr"));

	// Tính colIndex động theo vị trí thực tế của th
	const headers = Array.from(thElement.parentElement.children);
	const colIndex = headers.indexOf(thElement);

	const asc = table.dataset.sortCol == colIndex && table.dataset.sortDir !== "asc";
	table.dataset.sortCol = colIndex;
	table.dataset.sortDir = asc ? "asc" : "desc";

	const parseMoney = (str) => parseFloat(str.replace(/\./g, '').replace(/,/g, '.')) || 0;
	const parseLit = (str) => parseFloat(str.replace(",", ".")) || 0;
	const parseTime = (str) => {
		const parts = str.split(":").map(Number);
		return parts.length === 3 ? parts[0] * 3600 + parts[1] * 60 + parts[2] : 0;
	};

	rows.sort((a, b) => {
		let v1 = a.cells[colIndex]?.textContent.trim() || '';
		let v2 = b.cells[colIndex]?.textContent.trim() || '';

		let n1 = v1, n2 = v2;

		if (v1.includes("₫") || v1.match(/^\d{1,3}(\.\d{3})*(,\d+)?$/)) {
			n1 = parseMoney(v1);
			n2 = parseMoney(v2);
		} else if (v1.match(/^\d+,\d+$/) || v1.match(/^\d{1,3}(,\d{3})*$/)) {
			n1 = parseLit(v1);
			n2 = parseLit(v2);
		} else if (v1.match(/^\d{1,2}:\d{2}:\d{2}$/)) {
			n1 = parseTime(v1);
			n2 = parseTime(v2);
		} else {
			n1 = v1.toLowerCase();
			n2 = v2.toLowerCase();
		}

		if (typeof n1 === "string") {
			return asc ? n1.localeCompare(n2) : n2.localeCompare(n1);
		} else {
			return asc ? n1 - n2 : n2 - n1;
		}
	});

	tbody.innerHTML = "";
	rows.forEach(row => tbody.appendChild(row));
}


// Nút trạng thái đang cập nhật log
function updateFetchStatus(status) {
  const el = document.getElementById("fetchStatus");
  if (!el) return;
  el.innerText = status;
}

// ===== Phân trang hiển thị log ====================
function renderPagination() {
  const totalLogs = currentFilteredLogs.length;
  const totalPages = Math.ceil(totalLogs / logsPerPage);

  // Hiển thị thông tin
  const start = (currentPage - 1) * logsPerPage + 1;
  const end = Math.min(currentPage * logsPerPage, totalLogs);
  document.getElementById("paginationInfo").textContent = `Đang xem ${start} - ${end}/${totalLogs} giao dịch`;

  // Tạo số trang
  const numbers = [];
  for (let i = 1; i <= totalPages; i++) {
    numbers.push(`<button onclick="goToPage(${i})" class="${i === currentPage ? 'active' : ''}">${i}</button>`);
  }

  document.getElementById("paginationNumbers").innerHTML = numbers.slice(0, 10).join('');
}

function renderTableByPage() {
  const start = (currentPage - 1) * logsPerPage;
  const end = currentPage * logsPerPage;
  renderTable(currentFilteredLogs.slice(start, end));
}

function goToPage(page) {
  currentPage = page;
  renderPagination();
  renderTableByPage();
}

function goToFirstPage() {
  currentPage = 1;
  renderPagination();
  renderTableByPage();
}

function goToLastPage() {
  const totalPages = Math.ceil(currentFilteredLogs.length / logsPerPage);
  currentPage = totalPages;
  renderPagination();
  renderTableByPage();
}

function goToPrevPage() {
  if (currentPage > 1) currentPage--;
  renderPagination();
  renderTableByPage();
}

function goToNextPage() {
  const totalPages = Math.ceil(currentFilteredLogs.length / logsPerPage);
  if (currentPage < totalPages) currentPage++;
  renderPagination();
  renderTableByPage();
}

function changeLogsPerPage() {
  logsPerPage = Number(document.getElementById("logsPerPageSelect").value);
  currentPage = 1;
  renderPagination();
  renderTableByPage();
  fetchLogs();  // hoặc render lại trang
}

function showModal(id) {
  const el = document.getElementById(id);
  if (el) el.classList.add("show");
}
window.showModal = showModal;


// ===== Thanh toán QR Code ====================
async function generateQR(code) {
  const log = allLogs.find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy giao dịch.");

  let accountNo = "", accountName = "", acqId = "", branch = "";
  try {
    const res = await fetch("/api/bank");
    const bank = await res.json();
    accountNo = bank.qrAccountNo || "************";
    accountName = bank.qrAccountName || "Tên tài khoản";
    acqId = bank.qrBankCode || "970403";
    branch = bank.qrBankBranch || "";
  } catch (e) {
    return alert("❌ Không thể lấy thông tin ngân hàng.");
  }


  const fuelName = getMappedFuelName(log.fuelType) || "Không rõ";
  const cost = log.transactionCost;
  const volume = log.transactionAmount;
  const price = log.transactionPrice;
  const addInfo = `THANH TOAN CHO GIAO DICH ${code}`;
  const encodedName = encodeURIComponent(accountName);
  const encodedInfo = encodeURIComponent(addInfo);


  const qrImageUrl = `https://img.vietqr.io/image/${acqId}-${accountNo}-compact.png?accountName=${encodedName}&addInfo=${encodedInfo}&amount=${cost}`;

  Swal.fire({
	  html: `
		<div style="text-align:center">
		  <h5 style="margin-bottom: 4px;">THÔNG TIN CHUYỂN KHOẢN</h5>
		  <div style="font-weight: bold; font-size: 15px; color: #333;">
			${accountName}<br/>
			STK: ${accountNo}<br/>
			<!--${branch}-->
		  </div>

		  <img id="qrImage" src="${qrImageUrl}" style="max-width: 100%; margin: 8px 0; border-radius: 8px;" />
		  <p style="font-size: 13px; font-weight: bold; color: #dc3545;">${addInfo}</p>

		  <div style="font-size: 14px; text-align: left; margin: 12px auto; max-width: 320px;">
			<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
			  <span style="font-weight: bold; color: black;">Nhiên liệu:</span>
			  <span style="font-weight: bold; color: #063970;">${fuelName}</span>
			</div>
			<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
			  <span style="font-weight: bold; color: black;">Số tiền:</span>
			  <span style="font-weight: bold; color: #063970;">${cost.toLocaleString('vi-VN')} đồng</span>
			</div>
			<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
			  <span style="font-weight: bold; color: black;">Số lít:</span>
			  <span style="font-weight: bold; color: #063970;">${formatLit(volume)} lít</span>
			</div>
			<div style="display: flex; justify-content: space-between;">
			  <span style="font-weight: bold; color: black;">Đơn giá:</span>
			  <span style="font-weight: bold; color: #063970;">${price.toLocaleString('vi-VN')} đồng/lít</span>
			</div>
		  </div>

		  <div style="margin-top: 12px; text-align: center;">
			<button id="btnMarkPaid" style="background-color: #198754; color: white; border: none; padding: 6px 14px; border-radius: 6px; margin-right: 10px;">
			  Đã chuyển khoản
			</button>
			<button id="btnCloseQR" style="background-color: #6c757d; color: white; border: none; padding: 6px 14px; border-radius: 6px;">
			  Đóng
			</button>
		  </div>
		</div>
	  `,
    showConfirmButton: false,
    showCloseButton: true,
    width: 380,

    didOpen: () => {
		document.getElementById("btnMarkPaid").onclick = async () => {
		  const qrIcon = document.getElementById(`qr-icon-${code}`);
		  if (qrIcon) {
			qrIcon.classList.remove("text-primary");
			qrIcon.classList.add("text-success");
		  }

		  const res = await fetch("/api/mark_qr_paid", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({ transactionCode: code })
		  });

		  const result = await res.json();
		  if (result.success) {
			Swal.fire({
			  icon: "success",
			  title: "Đã ghi nhận thanh toán",
			  text: "Biểu tượng QR đã đổi màu xanh lá.",
			  timer: 2500,
			  showConfirmButton: true
			});
			await fetchLogs();
		  } else {
			Swal.fire({
			  icon: "error",
			  title: "❌ Lỗi",
			  text: result.message || "Không thể ghi nhận thanh toán.",
			});
		  }
		};

        document.getElementById("btnCloseQR").onclick = () => {
          Swal.close();
        };
    }
		
  });
}

// === Tự F5 index khi qua ngày mới ======================
function scheduleReloadAtMidnight() {
  const now = new Date();
  const midnight = new Date();

  midnight.setHours(24, 0, 0, 0); // 00:00:00 ngày mai

  const msUntilMidnight = midnight.getTime() - now.getTime();

  console.log(`⏰ Tự động F5 sau ${(msUntilMidnight / 1000 / 60).toFixed(2)} phút`);

  setTimeout(() => {
    console.log("🔁 Đến ngày mới, đang F5 lại trang...");
    location.reload();
  }, msUntilMidnight);
}
scheduleReloadAtMidnight();

// === Xử lý đếm số lượng người truy cập ======================
async function showActiveUsers() {
  try {
    const res = await fetch('/active_users');
    const data = await res.json();
    document.getElementById('activeUserCount').innerText = `🟢 Online: ${data.active}`;
  } catch (e) {
    console.warn("Không thể lấy số người dùng hoạt động.");
  }
}

setInterval(showActiveUsers, 15000); // cập nhật mỗi 15 giây

window.showModal = showModal;


</script>


<style>
  .swal2-container {
    z-index: 10000 !important;
  }
</style>

</body>
</html>
