import pymysql
import os
import pwinput

def show_databases(cursor):
    cursor.execute("SHOW DATABASES;")
    dbs = [row[0] for row in cursor.fetchall()]
    print("\n===> DANH SACH DATABASE HIEN CO:")
    for i, db in enumerate(dbs):
        print(f"  [{i+1}] {db}")
    return dbs

def view_database(cursor, db_name):
    try:
        cursor.execute(f"USE `{db_name}`;")
        cursor.execute("SHOW TABLES;")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"\nCAC BANG TRONG `{db_name}`:")
        for tbl in tables:
            print(f"\n  ▸ {tbl}")

            # Hiện số lượng bản ghi
            try:
                cursor.execute(f"SELECT COUNT(*) FROM `{tbl}`;")
                count = cursor.fetchone()[0]
                print(f"     So dong: {count}")
            except Exception as e:
                print(f"     Khong dem duoc so dong: {e}")

            # Hi<PERSON><PERSON> danh sách cột
            cursor.execute(f"SHOW COLUMNS FROM `{tbl}`;")
            columns = cursor.fetchall()
            for col in columns:
                print(f"     - {col[0]} ({col[1]})")
    except Exception as e:
        print(f"Khong the doc database `{db_name}`:", e)

def view_invoiced_logs_by_date(cursor, db_name):
    try:
        cursor.execute(f"USE `{db_name}`;")

        # Kiểm tra bảng log tồn tại
        cursor.execute("SHOW TABLES LIKE 'log';")
        if not cursor.fetchone():
            print("Bang `log` khong ton tai trong database.")
            return

        print(f"\nTHONG KE LOG DA XUAT HOA DON THEO NGAY - `{db_name}`")

        cursor.execute("""
            SELECT transactionDate,
                   COUNT(*) AS tong_giao_dich,
                   SUM(isInvoiced = 1) AS da_xuat,
                   SUM(isInvoiced = 0) AS chua_xuat
            FROM log
            GROUP BY transactionDate
            ORDER BY transactionDate DESC;
        """)

        rows = cursor.fetchall()

        if not rows:
            print("Khong co du lieu trong bang `log`.")
            return

        print(f"\n{'Ngay':12} {'Tong':>8} {'Da xuat':>10} {'Chua':>8}")
        print("-" * 40)
        for row in rows:
            print(f"{row[0]:12} {row[1]:>8} {row[2]:>10} {row[3]:>8}")

    except Exception as e:
        print("Loi khi truy van du lieu:", e)


def export_database(user, password, db_name):
    try:
        conn = pymysql.connect(host="localhost", user=user, password=password, db=db_name, charset="utf8mb4")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM log;")
        count = cursor.fetchone()[0]
        print(f"So log hien co: {count}")
    except Exception as e:
        print("Khong the kiem tra du lieu:", e)

    filename = f"{db_name}_backup.sql"
    mysqldump_path = "C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqldump.exe"
    command = f'"{mysqldump_path}" -u {user} -p{password} {db_name} > "{filename}"'

    result = os.system(f'cmd /c {command}')

    if result == 0:
        print(f"Da export database `{db_name}` ra file `{filename}`.")
    else:
        print("Loi export database.")


def import_database(user, password, _unused):
    print("Tu dong khoi phuc file backup.sql trong thu muc du an...")

    sql_files = [f for f in os.listdir(".") if f.endswith(".sql")]
    if not sql_files:
        print("Khong tim thay file .sql trong thu muc du an.")
        return

    print("\nCac file .sql tim thay:")
    for i, f in enumerate(sql_files):
        print(f"  [{i+1}] {f}")

    choice = input("Nhap so thu tu file sql muon import: ").strip()
    if not choice.isdigit() or int(choice) < 1 or int(choice) > len(sql_files):
        print("So thu tu khong hop le.")
        return

    filename = sql_files[int(choice) - 1]

    target_db = input("Nhap ten database muon import vao (Vi du: auco): ").strip()
    if not target_db:
        print("Ban chua nhap ten database.")
        return

    # 👉 Kết nối và tạo database nếu chưa có
    try:
        conn = pymysql.connect(host="localhost", user=user, password=password, charset="utf8mb4", autocommit=True)
        cursor = conn.cursor()
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{target_db}` CHARACTER SET utf8mb4;")
        print(f"Database `{target_db}` da san sang.")

        cursor.execute(f"USE `{target_db}`;")

        # ✅ Tạo bảng station
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS station (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stationCode VARCHAR(20) NOT NULL UNIQUE,
            stationName VARCHAR(100) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)

        # ✅ Tạo bảng log
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dataType INT DEFAULT 2,
            verificationCode VARCHAR(50) DEFAULT 'N/A',
            transactionCode VARCHAR(30) NOT NULL UNIQUE,
            partnerCode VARCHAR(20) DEFAULT 'P01',
            companyCode VARCHAR(20) DEFAULT 'C01',
            storeCode VARCHAR(20) DEFAULT 'S01',
            station_id INT,
            hardwareId VARCHAR(12) DEFAULT 'N/A',
            faucetNo INT DEFAULT 0,
            pumpSerial INT DEFAULT 0,
            fuelType VARCHAR(10) DEFAULT 'N/A',
            transactionDate VARCHAR(10) DEFAULT 'N/A',
            transactionTime VARCHAR(8) DEFAULT 'N/A',
            transactionCost INT DEFAULT 0,
            transactionAmount INT DEFAULT 0,
            transactionPrice INT DEFAULT 0,
            shiftCost BIGINT DEFAULT 0,
            shiftAmount BIGINT DEFAULT 0,
            shiftNumberOfTransactions BIGINT DEFAULT 0,
            totalCost BIGINT DEFAULT 0,
            totalAmount BIGINT DEFAULT 0,
            totalNumberOfTransactions BIGINT DEFAULT 0,
            isInvoiced BOOLEAN DEFAULT FALSE,
            invoiceNumber VARCHAR(50),
            internalNote TEXT,
            provider VARCHAR(20),
            invoiceId VARCHAR(50),
            qr_paid BOOLEAN DEFAULT FALSE,
            CONSTRAINT fk_station FOREIGN KEY (station_id) REFERENCES station(id),
            CHECK (transactionCost BETWEEN 0 AND **********),
            CHECK (transactionAmount BETWEEN 0 AND **********),
            CHECK (transactionPrice BETWEEN 0 AND **********),
            CHECK (shiftCost BETWEEN 0 AND **********),
            CHECK (shiftAmount BETWEEN 0 AND **********),
            CHECK (shiftNumberOfTransactions BETWEEN 0 AND **********),
            CHECK (totalCost BETWEEN 0 AND **********),
            CHECK (totalAmount BETWEEN 0 AND **********),
            CHECK (totalNumberOfTransactions BETWEEN 0 AND **********)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)

        # ✅ Tạo bảng customer
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS customer (
            id INT AUTO_INCREMENT PRIMARY KEY,
            mkhang VARCHAR(50),
            buyer VARCHAR(100),
            company VARCHAR(200),
            address VARCHAR(300),
            tax VARCHAR(20),
            email VARCHAR(100),
            phone VARCHAR(20),
            plate VARCHAR(20),
            mdvqhns VARCHAR(20)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)

        # ✅ Tạo bảng user với thêm api_account và api_pass
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS user (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password_hash VARCHAR(300) NOT NULL,
            role VARCHAR(10) DEFAULT 'user',
            api_account VARCHAR(100),
            api_pass VARCHAR(100)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)
        
        # ✅ Tạo bảng invoice_log để lưu toàn bộ custom_data khi xuất hóa đơn
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS invoice_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transaction_code VARCHAR(50) NOT NULL,
            invoice_number VARCHAR(50) NOT NULL,
            custom_data JSON,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """)

        print(f"Da tao day du bang: station, log, customer trong `{target_db}`.")

        # 🔧 Nhập thông tin trạm
        station_code = input("Nhap stationCode = StoreCode (vi du: AUCO): ").strip().upper()
        station_name = input("Nhap ten tram (Vi du: CHXD Au Co): ").strip()

        if station_code and station_name:
            cursor.execute("INSERT IGNORE INTO station (stationCode, stationName) VALUES (%s, %s);",
                           (station_code, station_name))
            print(f"Da them stationCode (StoreCode) `{station_code}` vao bang station.")
        else:
            print("Bo qua them stationCode (StoreCode) vi thieu thong tin.")

    except Exception as e:
        print(f"Loi khi tao database hoac bang: {e}")
        return

    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

    print(f"Dang import file `{filename}` vao database `{target_db}`...")
    command = f'mysql -u {user} -p"{password}" {target_db} < "{filename}"'
    result = os.system(command)

    if result == 0:
        print(f"Da import thanh cong file `{filename}` vào `{target_db}`.")
    else:
        print("Loi khi import file.")



def delete_database(cursor, db_name):
    confirm = input(f"Ban chac chan muon xoa `{db_name}`? (yes/no): ").strip().lower()
    if confirm == "yes":
        cursor.execute(f"DROP DATABASE `{db_name}`;")
        print(f"Da xoa database `{db_name}`.")
    else:
        print("Da huy xoa.")

def main():
    print("QUAN LY DATABASE MySQL – MontechPOS")

    user = "root"
    password = pwinput.pwinput("Nhap mat khau MySQL (user=root): ")

    try:
        conn = pymysql.connect(host="localhost", user=user, password=password, charset="utf8mb4", autocommit=True)
        cursor = conn.cursor()

        while True:
            dbs = show_databases(cursor)

            print("\nMENU:")
            print("  1. Tao database moi")
            print("  2. Xem bang va cot trong database")
            print("  3. Xem log da xuat hoa don")
            print("  4. Export database (sao luu)")
            print("  5. Import database (khoi phuc)")
            print("  6. Xoa database")
            print("  0. Thoat")

            choice = input("\nNhap lua chon: ").strip()

            if choice == "1":
                new_db = input("Nhap ten database moi: ").strip()
                if new_db:
                    cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{new_db}` CHARACTER SET utf8mb4;")
                    print(f"Da tao database `{new_db}`.")

                    cursor.execute(f"USE `{new_db}`;")

                    # Tạo bảng station
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS station (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        stationCode VARCHAR(20) NOT NULL UNIQUE,
                        stationName VARCHAR(100) NOT NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    """)

                    # Tạo bảng log
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS log (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        dataType INT DEFAULT 2,
                        verificationCode VARCHAR(50) DEFAULT 'N/A',
                        transactionCode VARCHAR(30) NOT NULL UNIQUE,
                        partnerCode VARCHAR(20) DEFAULT 'P01',
                        companyCode VARCHAR(20) DEFAULT 'C01',
                        storeCode VARCHAR(20) DEFAULT 'S01',
                        station_id INT,
                        hardwareId VARCHAR(12) DEFAULT 'N/A',
                        faucetNo INT DEFAULT 0,
                        pumpSerial INT DEFAULT 0,
                        fuelType VARCHAR(10) DEFAULT 'N/A',
                        transactionDate VARCHAR(10) DEFAULT 'N/A',
                        transactionTime VARCHAR(8) DEFAULT 'N/A',
                        transactionCost INT DEFAULT 0,
                        transactionAmount INT DEFAULT 0,
                        transactionPrice INT DEFAULT 0,
                        shiftCost BIGINT DEFAULT 0,
                        shiftAmount BIGINT DEFAULT 0,
                        shiftNumberOfTransactions BIGINT DEFAULT 0,
                        totalCost BIGINT DEFAULT 0,
                        totalAmount BIGINT DEFAULT 0,
                        totalNumberOfTransactions BIGINT DEFAULT 0,
                        isInvoiced BOOLEAN DEFAULT FALSE,
                        invoiceNumber VARCHAR(50),
                        internalNote TEXT,
                        provider VARCHAR(20),
                        invoiceId VARCHAR(50),
                        qr_paid BOOLEAN DEFAULT FALSE,
                        CONSTRAINT fk_station FOREIGN KEY (station_id) REFERENCES station(id),
                        CHECK (transactionCost BETWEEN 0 AND **********),
                        CHECK (transactionAmount BETWEEN 0 AND **********),
                        CHECK (transactionPrice BETWEEN 0 AND **********),
                        CHECK (shiftCost BETWEEN 0 AND **********),
                        CHECK (shiftAmount BETWEEN 0 AND **********),
                        CHECK (shiftNumberOfTransactions BETWEEN 0 AND **********),
                        CHECK (totalCost BETWEEN 0 AND **********),
                        CHECK (totalAmount BETWEEN 0 AND **********),
                        CHECK (totalNumberOfTransactions BETWEEN 0 AND **********)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    """)

                    # Tạo bảng customer
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS customer (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        mkhang VARCHAR(50),
                        buyer VARCHAR(100),
                        company VARCHAR(200),
                        address VARCHAR(300),
                        tax VARCHAR(20),
                        email VARCHAR(100),
                        phone VARCHAR(20),
                        plate VARCHAR(20),
                        mdvqhns VARCHAR(20)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    """)
                    
                    # Tạo bảng user với thêm api_account và api_pass
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS user (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        username VARCHAR(50) NOT NULL UNIQUE,
                        password_hash VARCHAR(300) NOT NULL,
                        role VARCHAR(10) DEFAULT 'user',
                        api_account VARCHAR(100),
                        api_pass VARCHAR(100)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    """)
                    
                    # Tạo bảng invoice_log để lưu toàn bộ custom_data khi xuất hóa đơn
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS invoice_log (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        transaction_code VARCHAR(50) NOT NULL,
                        invoice_number VARCHAR(50) NOT NULL,
                        custom_data JSON,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    """)

                    print(f"Da tao day du bang: station, log, customer trong `{new_db}`.")
                    station_code = input("Nhap stationCode = StoreCode (vi du: AUCO): ").strip().upper()
                    station_name = input("Nhap ten tram (Vi du: CHXD Au Co): ").strip()
                    cursor.execute("INSERT IGNORE INTO station (stationCode, stationName) VALUES (%s, %s);", (station_code, station_name))
                    print(f"Da them stationCode (StoreCode) `{station_code}` vao bang `station` trong DB `{new_db}`.")

            elif choice in ["2", "3", "4", "6"]:
                idx = input("Nhap so thu tu database: ").strip()
                if not idx.isdigit() or int(idx) < 1 or int(idx) > len(dbs):
                    print("So thu tu khong hop le.")
                    continue
                selected_db = dbs[int(idx) - 1]
                if choice == "2":
                    view_database(cursor, selected_db)
                elif choice == "3":
                    view_invoiced_logs_by_date(cursor, selected_db)
                elif choice == "4":
                    export_database(user, password, selected_db)
                elif choice == "6":
                    delete_database(cursor, selected_db)
            
            elif choice == "5":
                import_database(user, password, "")        
            elif choice == "0":
                print("Thoat chuong trinh.")
                break
            else:
                print("Lua chon khong hop le.")

    except Exception as e:
        print("Loi ket noi hoac thuc thi:", e)
    finally:
        try:
            cursor.close()
            conn.close()
        except:
            pass

if __name__ == "__main__":
    main()
