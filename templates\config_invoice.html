<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON> hình hóa đơn</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <style>
	html, body {
	  height: 100%;
	  margin: 0;
	  background: #f8f9fa;
	  font-family: 'Arial', sans-serif;
	  overflow: auto;
	}

	.setting-container {
	  max-width: 100%;
	  height: 100vh;
	  margin: 0 auto;
	  padding: 30px 40px;
	  background: #f8f9fa;
	  border-radius: 16px;
	  display: flex;
	  flex-direction: column;
	}


    label {
      font-weight: bold;
    }

    h3.title {
      color: #dc3545;
      font-weight: bold;
      text-align: center;
      margin-bottom: 1px;
	  margin-top: 1px;
    }
	button.btn {
	  width: 22%;
	  height: 52px;
	  font-size: 18px;
	  background-color: #063970;
	  color: white;
	  border: none;
	  border-radius: 8px;
	}
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body>
  <div class="setting-container">
    <h3 class="title">CẤU HÌNH ĐƠN VỊ HÓA ĐƠN</h3>
	<hr class="my-1" style="border-top: 3px solid #0d6efd;" />
    <div class="mb-2">
      <label for="provider">Chọn nhà cung cấp:</label>
      <select id="provider" class="form-select" onchange="loadProviderFields()">
        <option value="VNPT">VNPT</option>
        <option value="MOBIFONE">MOBIFONE</option>
        <option value="VIETTEL">VIETTEL</option>
		<option value="MISA">MISA</option>
		<option value="EASYINVOICE">EASYINVOICE</option>
	    <option value="BKAV">BKAV</option>
		<option value="FAST">FAST</option>
		<option value="HILO">HILO</option>
		<option value="MOBIFONE_TEST">MOBIFONE_TEST</option>
      </select>
    </div>

    <div id="configFields" class="row g-3"></div>

    <div class="mt-4 text-center">
      <button class="btn btn-primary px-4" onclick="saveConfig()">LƯU CẤU HÌNH</button>
    </div>
  </div>

  <script>
    const fieldDefs = {
      VNPT: [
        "Account",
		"ACpass",
		"username",
		"password",
		"link_api",
		"link_api_fkey",
        "pattern",
		"serial",
		"convert",
		"invoice_type",
		"NoDigitofPrice",
        "sellerFullName",
		"sellerName",
		"sellerTaxCode",
		"sellerAddress"
      ],
      MOBIFONE: [
        "tax_code",
		"username",
		"password",
		"template_code",
		"login_url",
		"get_cctbao_url",
		"link_api",
		"link_api_fkey",
		"pdf_url",
        "sellerFullName",
		"sellerName",
		"sellerTaxCode",
		"sellerAddress"
      ],
      MOBIFONE_TEST: [
        "tax_code",
		"username",
		"password",
		"ma_dvcs",
		"login_url",
		"get_cctbao_url",
		"link_api",
		"generate_url",
		"cctbao_id",
		"template_code",
		"sign_mode",
		"invoice_type",
		"NoDigitofPrice",
        "sellerFullName",
		"sellerName",
		"sellerTaxCode",
		"sellerAddress"
      ],
      BKAV: [
        "partnerGUID",
        "partnerToken",
        "env_testing",
        "url_api_link",
        "url_api_link_test",
        "url_link_PDF",
        "cmd_type",
        "invoice_type_id",
        "currency_id",
        "exchange_rate",
        "no_digit_of_price",
        "sellerFullName",
        "sellerName",
        "sellerTaxCode",
        "sellerAddress"
      ],
      VIETTEL: [
        "tax_code",
        "username",
        "password",
        "url_api_link",
        "env_testing",
        "invoice_type",
        "template_code",
        "invoice_series",
        "exchange_rate",
        "validation",
        "no_digit_of_price",
        "sellerFullName",
        "sellerName",
        "sellerTaxCode",
        "sellerAddress"
      ],
	  
    };

    async function loadProviderFields() {
      const res = await fetch('/load_config_invoice');
      const json = await res.json();
      const provider = document.getElementById("provider").value;
      const upperProvider = provider.toUpperCase();

      const container = document.getElementById("configFields");
      container.innerHTML = "";

      const fields = fieldDefs[upperProvider] || [];

      const sellerGroup = document.createElement("div");
      sellerGroup.className = "row g-4 mt-2";
      sellerGroup.innerHTML = `
		<hr class="my-1" style="border-top: 3px solid #0d6efd;" />
        <h3 class=\"title\">THÔNG TIN NGƯỜI BÁN</h3>
      `;

      for (let key of fields) {
        const isSellerField = key.toLowerCase().includes("seller");
        const formGroup = document.createElement("div");
        formGroup.className = "col-md-6";
        formGroup.innerHTML = `
          <label for="${key}">${key}</label>
          <input type="text" class="form-control" id="${key}">
        `;

        if (isSellerField) {
          sellerGroup.appendChild(formGroup);
        } else {
          container.appendChild(formGroup);
        }
      }

      if (sellerGroup.children.length > 1) {
        container.appendChild(sellerGroup);
      }

      const conf = json[upperProvider] || {};
      for (let key in conf) {
        const input = document.getElementById(key);
        if (input) input.value = conf[key];
      }
    }

    async function saveConfig() {
	  const provider = document.getElementById("provider").value.toUpperCase();
	  const fields = fieldDefs[provider] || [];

	  const payload = {
		provider: provider,
		[provider]: {}
	  };

	  for (let key of fields) {
		payload[provider][key] = document.getElementById(key)?.value || "";
	  }

	  const res = await fetch('/save_config_invoice', {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify(payload)
	  });

	  const result = await res.json();
	  alert(result.message);

	  // ✅ Gọi restart auto_scheduler.py giống setting.html
	  try {
		await fetch("/api/restart_scheduler");
		console.log("🔁 Đã yêu cầu restart auto_scheduler.py");
	  } catch (e) {
		console.warn("❌ Lỗi khi restart auto_scheduler.py:", e);
	  }

	  // ✅ Thông báo cho các tab khác reload lại config
	  const timestamp = Date.now().toString();
	  localStorage.setItem("configInvoiceUpdatedAt", timestamp);
	  window.dispatchEvent(new StorageEvent("storage", {
		key: "configInvoiceUpdatedAt",
		newValue: timestamp
	  }));
	}


	window.onload = async () => {
	  try {
		const res = await fetch("/load_config_invoice");
		const config = await res.json();

		const provider = config.provider?.toUpperCase() || "VNPT";
		const providerSelect = document.getElementById("provider");
		if (providerSelect) providerSelect.value = provider;

		await loadProviderFields();  // tự động load fields theo provider đang dùng
	  } catch (e) {
		console.error("❌ Không load được cấu hình hóa đơn:", e);
	  }
	};

  </script>
</body>
</html>
