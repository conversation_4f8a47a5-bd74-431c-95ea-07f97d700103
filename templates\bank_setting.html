<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="utf-8" />
  <title>Thanh toán QR</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
  <style>
    body {
      background: #f8f9fa;
      font-family: 'Arial', sans-serif;
    }

    .setting-container {
      max-width: 960px;
      margin: 20px auto;
      background: white;
      padding: 30px 20px;
      border-radius: 16px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    label {
      font-weight: bold;
      margin-bottom: 6px;
    }

    h3.title {
      color: #0d6efd;
      font-weight: 700;
      text-align: center;
      margin-bottom: 24px;
      font-size: 24px;
    }

    .form-control {
      font-size: 16px;
      padding: 8px 12px;
    }

    .btn-primary {
      font-size: 16px;
      padding: 10px;
    }
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body>

  <div class="setting-container">

	<h3 class="title" style="color: #dc3545; font-size: 20px; font-weight: bold; text-align: center;">CÀI ĐẶT NGÂN HÀNG</h3>


	<div class="row g-4">
	<div class="col-12">
	  <label for="qrBankBranch">Tên ngân hàng:</label>
	  <select id="qrBankBranch" class="form-control" onchange="updateBankCode()">
		<option value="">-- Chọn ngân hàng --</option>
		<option value="970416">ACB Ngân hàng TMCP Á Châu</option>
		<option value="970425">ABB Ngân hàng TMCP An Bình</option>
		<option value="970409">BAB Ngân hàng TMCP Bắc Á</option>
		<option value="970418">BIDV Đầu tư & Phát triển</option>
		<option value="970438">BVB (Bảo Việt Bank)</option>
		<option value="546034">CAKE (Ngân hàng số Cake by VPBank)</option>
		<option value="970444">CBB (Xây dựng Việt Nam)</option>
		<option value="422589">CIMB</option>
		<option value="533948">Citibank chi nhánh Hà Nội</option>
		<option value="970446">COOPBANK (Hợp tác xã VN)</option>
		<option value="796500">DBS chi nhánh TP.HCM</option>
		<option value="970406">DongA Bank Đông Á</option>
		<option value="970431">Eximbank EIB /option>
		<option value="970408">GPB (Global PetroBank)</option>
		<option value="970437">HDBank</option>
		<option value="970442">HLBVN (HongLeong)</option>
		<option value="458761">HSBC</option>
		<option value="970456">IBK-HCM (Công nghiệp Hàn Quốc TP.HCM)</option>
		<option value="970455">IBK-HN (Công nghiệp Hàn Quốc Hà Nội)</option>
		<option value="970415">VietinBank ICB</option>
		<option value="970434">Indovina IVB </option>
		<option value="970463">KBHCM (Kookmin TP.HCM)</option>
		<option value="970462">KBHN (Kookmin Hà Nội)</option>
		<option value="668888">KBank (Kasikorn)</option>
		<option value="970466">KEBHana HCM</option>
		<option value="970467">KEBHana HN</option>
		<option value="970452">KLB (Kiên Long Bank)</option>
		<option value="970449">LPBank LienVietPostBank</option>
		<option value="970422">MB Quân đội</option>
		<option value="970426">MSB Hàng Hải</option>
		<option value="970428">NAB Nam A Bank</option>
		<option value="970419">NCB</option>
		<option value="801011">NHB-HN (NongHyup Hà Nội)</option>
		<option value="970448">OCB</option>
		<option value="970414">Oceanbank</option>
		<option value="970439">PBVN (Public Bank VN)</option>
		<option value="970430">PGB (Petrolimex)</option>
		<option value="970412">PVCB (Vietnam Public Bank)</option>
		<option value="970429">SCB Sài Gòn CB</option>
		<option value="970410">SCVN (Standard Chartered VN)</option>
		<option value="970440">SEAB (Đông Nam Á)</option>
		<option value="970400">SGICB (Sài Gòn Công Thương)</option>
		<option value="970443">SHB</option>
		<option value="970424">Shinhan SHBVN</option>
		<option value="970403">Sacombank STB</option>
		<option value="970407">Techcombank TCB</option>
		<option value="963388">TIMO (Ngân hàng số Timo)</option>
		<option value="970423">TPB (TPBank)</option>
		<option value="970458">UOB TP.HCM</option>
		<option value="546035">Ubank (VPBank số)</option>
		<option value="970427">VAB (VietA Bank)</option>
		<option value="970405">Agribank VBA </option>
		<option value="999888">VBSP (Chính sách Xã hội)</option>
		<option value="970436">Vietcombank VCB</option>
		<option value="970454">VCCB (Bản Việt)</option>
		<option value="970441">VIB</option>
		<option value="970433">Vietbank (VIETBANK)</option>
		<option value="971011">VNPT Money</option>
		<option value="970432">VPBank VPB</option>
		<option value="970421">VRB (Viet–Nga Bank)</option>
		<option value="971005">VTLMoney (Viettel Money)</option>
		<option value="970457">Woori Bank (WVN)</option>
	  </select>
	</div>
	
	<div class="col-12">
	  <label for="qrBankCode">Mã ngân hàng (BIN – acqId):</label>
	  <input id="qrBankCode" class="form-control" readonly placeholder="970407" />
	</div>

	  <div class="col-12">
		<label for="qrAccountNo">Số tài khoản:</label>
		<input id="qrAccountNo" class="form-control" placeholder="************" />
	  </div>

	  <div class="col-12">
		<label for="qrAccountName">Tên tài khoản:</label>
		<input id="qrAccountName" class="form-control" placeholder="CONG TY TNHH KY THUAT MON" />
	  </div>
	  
	  <div class="col-12 text-center mt-4">
		<button id="saveButton" style="
		  width: 40%;
		  height: 52px;
		  font-size: 18px;
		  background-color: #063970;
		  color: white;
		  border: none;
		  border-radius: 8px;
		" onclick="saveBankSettings()">LƯU CÀI ĐẶT</button>
	  </div>
	</div>
    
	</div>
  </div>

<script>
  function updateBankCode() {
    const dropdown = document.getElementById("qrBankBranch");
    const bin = dropdown.value;
    document.getElementById("qrBankCode").value = bin;
  }
  
  async function saveBankSettings() {
    const payload = {
      qrAccountNo: document.getElementById("qrAccountNo").value,
      qrAccountName: document.getElementById("qrAccountName").value,
      qrBankCode: document.getElementById("qrBankCode").value,
      qrBankBranch: document.getElementById("qrBankBranch").value
    };

    const res = await fetch("/api/bank", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload)
    });

    const result = await res.json();
    if (result.success) {
      alert("✅ Đã lưu cài đặt ngân hàng!");
      const timestamp = Date.now().toString();
      localStorage.setItem("bankSettingUpdatedAt", timestamp);
      window.dispatchEvent(new StorageEvent("storage", {
        key: "bankSettingUpdatedAt",
        newValue: timestamp
      }));
    } else {
      alert("❌ Không thể lưu cài đặt.");
    }
  }

  window.onload = async () => {
    try {
      const res = await fetch("/api/bank");
      const data = await res.json();
      document.getElementById("qrAccountNo").value = data.qrAccountNo || "";
      document.getElementById("qrAccountName").value = data.qrAccountName || "";
      document.getElementById("qrBankCode").value = data.qrBankCode || "";
      document.getElementById("qrBankBranch").value = data.qrBankBranch || "";
    } catch (e) {
      console.error("❌ Lỗi tải cấu hình ngân hàng:", e);
    }
  };
</script>

</body>
</html>
