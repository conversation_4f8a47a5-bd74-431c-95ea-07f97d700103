
/* ======= CẤU TRÚC CHUNG ======= */
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Roboto Mono' !important, sans-serif;
}

.container {
  max-width: 1200px;
  margin: auto;
  background: #ffffff;
  padding: 10px 30px 20px 30px;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.container-fluid {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ======= BẢNG LOG ======= */
.table-wrapper {
  height: 100vh;
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  border-radius: 10px;
  border: 1px solid #dee2e6;
}

#logTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  border-radius: 10px;
}

#logTable td {
  padding: 0.5px 2px;
  text-align: center;
  vertical-align: middle;
  background-color: #ffffff;
  transition: background 0.2s ease;
  border-bottom: 1px solid #dee2e6;
}

#logTable th {
  background-color: #063970 !important; #xanh navi
  padding: 5px 10px !important;
  font-weight: 400;
  text-align: center;
  color: #FFFFFF;
  text-transform: uppercase;
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 5;
  user-select: none;
}

#logTable tr:hover td {
  background-color: #efefef;
}

#logTable input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

#logTable button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  margin: 0 4px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

#logTable button:hover {
  background-color: #efefef;
}

#logTable .fa-eye { color: #fd7e14; }
#logTable .fa-paper-plane { color: #198754; }

th.sortable i.fa-sort {
  margin-left: 6px;
}


/* ======= HEADER THÔNG TIN CÔNG TY & TỔNG TIỀN ======= */
.company-header {
  text-align: left;
  color: #FF0004;
  font-size: 20px;
  margin-bottom: 2px;
  font-weight: bold;
  user-select: none;
}

.company-sub {
  text-align: left;
  color: #063970;
  font-size: 12px;
  margin-bottom: 12px;
  font-weight: bold;
  user-select: none;
}

.summary-bar {
  margin-top: 4px !important;
  margin-bottom: 6px !important;
  padding: 3px 10px !important;
  font-size: 12px;
  user-select: none;
}

.stat-number {
  color: #dc3545;
  font-weight: bold;
}


/* ======= KHỐI BỘ LỌC / NÚT XUẤT / LABEL ======= */
.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.controls label {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  white-space: nowrap;
  align-items: center;
  text-align: center;
}

.controls input[type="text"],
.controls input[type="date"],
.controls select {
  padding: 6px 10px;
  border-radius: 8px;
  border: 1px solid #ced4da;
  font-size: 13px;
  background-color: #f8f9fa;
  min-width: 120px;
}

.controls button {
  padding: 8px 18px;
  background-color: #4f8ef7;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  margin-left: auto;
}

.controls button:hover {
  background-color: #356ac3;
}

.form-label {
  color:  #063970;
  font-weight: 500;
  font-size: 13px;
  padding: 2px 8px;
  background-color: #efefef;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  user-select: none;
}

/* Thu nhỏ form-control trong filter */
.form-control,
.form-select {
  font-size: 12px !important;
  padding: 4px 6px !important;
  min-width: 90px !important;
  height: auto !important;
}


.form-4control {
  font-size: 12px !important;
  font-weight: 500 !important;
  padding: 4px 6px !important;
  min-width: 20px !important;
  max-width: 80px !important;
  height: auto !important;
}

.btn + .btn {
  margin-left: 5px;
}

.my-export-btn {
  background-color: #027419;
  color: white;
  border: none;
  font-weight: 500;
}

.my-export-btn:hover {
  background-color: #f9ff00;
}


/* ======= POPUP - MODAL XML PREVIEW ======= */
.modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100vw; height: 100vh;
  background-color: rgba(0,0,0,0.5);
  align-items: center;
  justify-content: center;
  z-index: 9999;
}


.modal-content {
  max-width: unset;
  width: 95vw;
}

.modal-content .row {
  width: 100%;
}

.modal.show {
  display: flex !important;
}

.close {
  position: absolute;
  top: 10px;
  right: 14px;
  font-size: 24px;
  cursor: pointer;
}


/* ======= FORM NHẬP TRONG POPUP XML ======= */
#searchCustomerPopup::placeholder {
  color: #999;      /* Màu xám mờ */
  opacity: 1;       /* Hiển thị rõ placeholder */
  font-style: italic; /* 👈 In nghiêng */
}

.invoice-form {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
}

.invoice-form .row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: space-between;
  margin-bottom: 3px; /* ✅ giảm từ 12px hoặc 16px xuống 6px */
}

.invoice-form .row label {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 180px;
  margin-bottom: 2px; /* ✅ chính dòng này giúp giảm khoảng cách */
}


.invoice-form label {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.invoice-form input,
.invoice-form select {
  padding: 10px 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 15px !important;
  height: 30px !important; /* ✅ thêm dòng này để tăng chiều cao */
  margin-bottom: 2px;
}


.invoice-form .btn-row button {
  flex: 1;
  padding: 5px;
  font-weight: bold;
  border: none;
  border-radius: 6px;
  background-color: #063970;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
}

.invoice-form .btn-row button:hover {
  background-color: #2563eb;
}

.hidden {
  display: none;
}


/* Làm viền bảng hóa đơn đậm hơn */
#previewContent table {
  border-collapse: collapse;
  width: 100%;
}

#previewContent th,
#previewContent td {
  border: 2px solid #333;  /* 👈 viền dày và rõ hơn */
  padding: 6px 8px;
}

#previewContent thead th {
  background-color: #f9f9f9;
}

/* Tiêu đề bảng hóa đơn */
.invoice-table thead tr {
  background-color: #063970 !important; /* Màu xanh đậm */
  color: white !important;              /* Chữ trắng */
  font-weight: bold;
}
/* ======= HEADER BẢNG HÓA ĐƠN ======= */
.invoice-table thead th {
  background-color: #063970 !important;  /* Màu nền xanh đậm */
  color: #ffffff !important;             /* Chữ trắng */
  font-weight: bold;
  text-align: center;
  border: 2px solid #333 !important;     /* Viền đậm */
}

/* ======= CÁC NÚT PHÂN TRANG ======= */
.pagination-buttons button {
  border: 1px solid #dee2e6;
  background-color: white;
  padding: 4px 10px;
  font-size: 13px;
  border-radius: 6px;
  cursor: pointer;
  min-width: 32px;
  transition: background-color 0.2s;
}

.pagination-buttons button:hover {
  background-color: #f1f1f1;
}

.pagination-buttons button.active {
  background-color: #0d6efd;
  color: white;
  border-color: #0d6efd;
  font-weight: bold;
}

/* ======= KHÓA GIAO DIỆN ======= */
.overlay-block {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0, 0, 0, 0.4); /* mờ nền tối */
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
}

.overlay-block .content {
  background: white;
  padding: 24px 32px;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  font-size: 20px;
  font-weight: bold;
  color: #c62828;
  text-align: center;
  max-width: 90%;
  line-height: 1.5;
}

/* ======= NÚT CÀI ĐẶT VÀ TÀI KHOẢN ======= */
.btn-setting {
  background-color: white !important;
  color:  #063970 !important;
  border: 1px solid #063970 !important; /* Đường viền đen, dày 1 pixel */
}

.btn-setting:hover {
  background-color: #0a4b8c !important; /* sáng hơn một chút */
  color: white !important;
}

.dropdown-menu .dropdown-item:hover {
	background-color: #0d6efd !important;
	color: white !important;
}
