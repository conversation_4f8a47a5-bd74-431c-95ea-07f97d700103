<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>MontechPOS</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"/>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    html, body {
	  height: 100%;
	  overflow: hidden; /* Không cho cuộn toàn trang */
      font-size: 15px;
      background: #f8f9fa;
	  display: flex;
	  flex-direction: column;
    }
    .container {
	  flex: 1;
	  overflow-y: auto;
	  padding-top: 0.5rem;
	  padding-bottom: 0.5rem;
	  margin-bottom: 0; /* dòng này để container không đẩy footer ra xa */
    }
	.container, .setting-container {
		max-width: 100%;
		margin: 0;
		padding: 5px; /* hoặc 0 nếu muốn sát viền tuyệt đối */
	}
    .form-label {
      margin-bottom: 2px;
      font-weight: 500;
	  display: flex;
      align-items: center;
      justify-content: center;
    }

    .form-control, .form-select {
      font-size: 14px;
      padding: 4px 8px;
    }
    .btn {
      padding-top: 6px;
      padding-bottom: 6px;
      font-size: 14px;
    }

    .summary-bar {
      background: white;
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 5px 12px;
      font-size: 13px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      row-gap: 6px;
      column-gap: 16px;
      line-height: 1;
    }
    .summary-bar div,
    .summary-bar label {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .summary-bar b {
      color: red;
    }
	#logTable th {
	  white-space: nowrap !important;
	  text-align: center !important;
	  vertical-align: middle !important;
	  padding: 6px 8px !important;
	  font-size: 13px;
	}
    .table th {
      font-size: 13px;
      background: #e9ecef;
      text-align: center;
      position: sticky;
      top: 0;
      z-index: 1;
    }
    .table td {
      font-size: 13px;
      white-space: nowrap;
      vertical-align: middle;
    }
    .table-responsive {
      max-height: 360px;
      overflow-y: auto;
    }
    .footer {
      text-align: center;
      font-size: 13px;
      color: gray;
      margin-top: 10px;
    }
	
    @media (max-width: 768px) {
    .modal-content {
      padding: 8px;
    }
	#xmlPreview .modal-content {
	margin-top: 0 !important;   /* Đẩy modal-content lên sát top */
	}

	#xmlPreview {
	  align-items: flex-start !important;  /* Đẩy cả modal ra top */
	  justify-content: center;
	  padding-top: 10px; /* nếu muốn hơi cách nhẹ 10px cho đẹp */
	}
    #previewContent {
      font-size: 12px;
      padding: 8px 0px;
	  min-height: 650px;
    }
    .preview-body {
      padding: 4px;
    }
	.invoice-form input,
	.invoice-form select {
	  margin-bottom: 0px !important; /* Siêu gọn */
	}
	.invoice-form .d-flex,
	#xmlFormPopup .modal-content > div {
	  background: none !important; /* Xóa mọi màu nền dư */
	}
	.invoice-form > div {
	  margin-bottom: 0px !important; /* Siêu gọn giữa các cụm label+input */
	}
    .invoice-table th, .invoice-table td {
      font-size: 12px;
      padding: 4px;
      word-break: break-word;
    }
	
	.modal-content {
	  overflow-y: auto;
	  max-height: 90vh; /* hoặc 95vh */
	}
	.footer {
	  text-align: center;
	  font-size: 12px;
	  color: #444;
	  padding: 2px 0; /* gọn gàng */
	  margin: 0;       /* bỏ luôn margin mặc định nếu có */
	  background: transparent; /* tùy bạn có thể có hoặc không */
	  flex-shrink: 0;  /* giữ footer cố định dưới */
	}

	

  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });
</script>
</head>
<body>
<div class="container py-2">
  <!-- Header -->
	<div class="d-flex flex-column align-items-center gap-2 mb-2 text-center">
	  <div>
		<h5 id="sellerName" style="font-size: 16px; font-weight: bold; color: red; margin: 0;">CÔNG TY MONTECH</h5>
			<p class="m-0" style="font-size: 12px; font-weight: bold; color: #063970; white-space: nowrap;">
			  <span id="sellerTaxcode">**********</span>
			  <span id="sellerFullName">Montech</span>
			  <span id="providerLabel" class="text-muted ms-1" style="font-weight: normal;"></span>
			</p>
	  </div>
	</div>

	<div class="d-flex justify-content-between align-items-center mb-2">
	  <!-- Trái: fetchStatus -->
	  <div id="fetchStatus" style="font-size: 13px; color: gray; text-align: center;"></div>
	  <div id="activeUserCount" style="font-size: 13px; color: gray; text-align: center;"></div>

	  <!-- Phải: Cài đặt + Tài khoản -->
	  <div class="d-flex align-items-center gap-2">
		<!-- Nút cài đặt có dropdown mở tab mới -->
		{% set username = session.get('username') %}
		{% set role = session.get('role') %}

		{% if role == 'admin' %}
		  <div class="dropdown">
			<button class="btn btn-sm dropdown-toggle btn-setting" data-bs-toggle="dropdown" title="Cài đặt hệ thống">
			  <i class="fa-solid fa-gear"></i> Cài đặt
			</button>
			<ul class="dropdown-menu dropdown-menu-end">
			  <li><a class="dropdown-item" href="/check_invoice" target="_blank">📊 Quản lý xuất hóa đơn</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/customer" target="_blank">🧑‍💼 Quản lý khách hàng</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/setting" target="_blank">🛠️ Cài đặt xuất tự động</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/config_invoice" target="_blank">🧬 Cấu hình hóa đơn</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/bank_setting" target="_blank">💳 Thanh toán QR</a></li>

			  {% if username == 'montech' %}
				<li><hr class="dropdown-divider"></li>
				<li><a class="dropdown-item" href="/check_log" target="_blank">🔐 Check giao dịch</a></li>
				<li><hr class="dropdown-divider"></li>
				<li><a class="dropdown-item" href="/debug" target="_blank">🧰 ️Check debug</a></li>
			  {% endif %}
			</ul>
		  </div>
		{% endif %}

		<!-- Icon user và tên -->
		<div class="dropdown">
		  <button class="btn btn-sm dropdown-toggle btn-setting d-flex align-items-center gap-2" data-bs-toggle="dropdown" title="Tài khoản">
			<i class="fa-solid fa-user"></i>
			<span>{{ session.get('username') }}</span>
		  </button>
		  <ul class="dropdown-menu dropdown-menu-end">
			{% if role == 'admin' %}
			  <li><a class="dropdown-item" href="/user_manage" target="_blank">🕵️‍♂️ Quản lý tài khoản</a></li>
			{% endif %}
			<li><hr class="dropdown-divider"></li>
			<li><a class="dropdown-item text-danger" href="/logout">👋 Đăng xuất</a></li>
		  </ul>
		</div>
	  
	  </div>
	
	</div>

  <!-- Bộ lọc -->
  <div class="row g-2 mb-2 text-center">
    <div class="col-3"><label class="form-label text-center d-block w-100">Ngày</label><input type="date" id="filterDate" class="form-control" /></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Từ giờ</label><input type="time" id="filterStartTime" class="form-control" /></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Đến giờ</label><input type="time" id="filterEndTime" class="form-control" /></div>
	<div class="col-3"><label class="form-label text-center d-block w-100">Tìm nhanh</label><input id="filterCode" class="form-control" placeholder="Tiền, Mã GD..." /></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Cò bơm</label><select id="filterFaucet" class="form-select"></select></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Nhiên liệu</label><select id="filterFuel" class="form-select"></select></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Đơn giá</label><select id="filterPrice" class="form-select"></select></div>
    <div class="col-3"><label class="form-label text-center d-block w-100">Bồn</label><select id="filterTank" class="form-select"></select></div>
  </div>

  <!-- Nút chức năng -->
  <div class="row g-2 mb-2 text-center">
    <div class="col-4"><button class="btn btn-primary w-100" onclick="fetchLogs()">Làm Mới</button></div>
    <div class="col-4"><button class="btn btn-warning w-100" onclick="sendBatchInvoices()">Xuất tự động</button></div>
    <!--<div class="col-3"><button class="btn btn-success w-100" onclick="exportExcelByDate()">Xuất File</button></div>-->
    <div class="col-4"><button class="btn btn-secondary w-100" onclick="markInternalLogs()">Nội Bộ</button></div>
  </div>

  <!-- Thống kê -->
  <div id="logSummary" class="summary-bar"></div>

  <!-- Bảng log mobile -->
  <div class="table-responsive mb-3">
    <table id="logTable" class="table table-bordered table-hover table-sm">
      <thead>
        <tr>
          <th><input type="checkbox" id="selectAll" onclick="toggleSelectAll(this)" /></th>
		  <th class="sortable" onclick="sortTable(this)">TIỀN<i class="fa-solid fa-sort"></i></th>
          <th class="sortable" onclick="sortTable(this)">LÍT<i class="fa-solid fa-sort"></i></th>
		  <th class="sortable" onclick="sortTable(this)">GIÁ<i class="fa-solid fa-sort"></i></th>
          <th>THAO TÁC</th>
		  <th>NHIÊN LIỆU</th>
          <th style="white-space: nowrap; text-align: center;" class="sortable" onclick="sortTable(this)">HĐ<i class="fa-solid fa-sort"></i></th> <!-- Không cho rớt dòng -->
          <th>NGÀY</th>
          <th class="sortable" onclick="sortTable(this)">GIỜ<i class="fa-solid fa-sort"></i></th>
		  <th>CÒ</th>
		  <th>SERI</th>
		  <th>BỒN</th>		  
		  <th>MÃ GD</th>
		  <th>TRẠM</th>
        </tr>
      </thead>
      <tbody id="logBody"></tbody>
    </table>
  </div>
  
  <!-- Phân trang 
	<div class="pagination-controls d-flex align-items-center justify-content-between mt-2" style="gap: 14px; flex-wrap: wrap; user-select: none;">-->
	<div class="pagination-controls d-none">
	  <select id="logsPerPageSelect" onchange="changeLogsPerPage()" class="form-select" style="width: 80px;">
		<option value="100">100</option>
		<option value="200">200</option>
		<option value="300">300</option>
		<option value="500">500</option>
		<option value="8000" selected>Tất cả</option>
	  </select>
	  
	  <div id="paginationInfo" style="font-size: 14px; color: #444;"></div>
	  
	  <div class="pagination-buttons d-flex align-items-center" style="gap: 4px;">
		<button onclick="goToFirstPage()">«</button>
		<button onclick="goToPrevPage()">‹</button>
		<span id="paginationNumbers"></span>
		<button onclick="goToNextPage()">›</button>
		<button onclick="goToLastPage()">»</button>
	  </div>
	</div> 
	
	<div style="display: flex; align-items: center; user-select: none; gap: 8px;">
		<div id="autoConfigStatus" style="font-size: 12px;"></div>
	</div> 	

</div>

<div class="footer" style="text-align: center; font-size: 12px; color: #444;">© Montech - 2025</div>	

<script>
  window.isAdmin = {{ is_admin|tojson }};
</script>

<script>
  window.onload = () => {
    loadDefaultCustomerData();  // gọi ngay khi load trang
  };
</script>

	<!-- Popup xem trước hóa đơn -->
	<div id="xmlPreview" class="modal hidden">
	  <div class="modal-content d-flex flex-column flex-md-row" style="gap: 16px; width: 100vw; max-width: 1200px; margin-top: 0 !important;">

		<!-- Cột trái: Form nhập liệu -->
		<div class="invoice-form" style="flex: 1;">
		  <div class="mb-1">
			<input id="searchCustomerPopup" class="form-control"
				   type="text"
				   enterkeyhint="enter"
				   placeholder="🔍 Tìm Mã KH, Cty, MST..."
				   oninput="searchCustomerPopup()"
				   onchange="autoFillCustomerByInput()"
				   list="customerList"
				   autocomplete="off" />

			<datalist id="customerList"></datalist>
		  </div>

		  <div class="d-flex gap-1 mb-1">
			  <div style="flex: 1;">
				<label>Tên người mua:</label>
				<input id="formHoTen" class="form-control" />
			  </div>
			  <div style="flex: 1;">
				<label>CCCD:</label>
				<input id="formCCCD" class="form-control" />
			  </div>
			  <div style="flex: 1;">
				<label>SĐT:</label>
				<input id="formPhone" class="form-control" />
			  </div>
		  </div>

		  <div class="mb-1">
			<label>Tên đơn vị:</label>
			<input id="formTen" class="form-control" />
		  </div>

		  <div class="mb-1">
			<label>Địa chỉ:</label>
			<input id="formDChi" class="form-control" />
		  </div>
			<!-- MST + Mã KH + Mã ĐVQHNS-->
			<div class="d-flex gap-1 mb-1">
			  <div style="flex: 1;">
				<label>MST:</label>
				<input id="formMST" class="form-control" />
			  </div>
			  <div style="flex: 1;">
				<label>Mã KH:</label>
				<input id="formMKHang" class="form-control" />
			  </div>
			  
			  <div style="flex: 1;">
				<label>Mã ĐVQHNS:</label>
				<input id="formMdvqhns" class="form-control" />
			  </div>
			</div>

			<!-- Thuế suất (%) + Hình thức thanh toán trên 1 dòng -->
			<div class="d-flex gap-1 mb-1">
			  <div style="flex: 1;">
				<label>Thuế(%):</label>
				<select id="formTThue" class="form-control">
				  <option value="10">10%</option>
				  <option value="8">8%</option>
				  <option value="5">5%</option>
				  <option value="0">0%</option>
				  <option value="KCT">KCT</option>
				  <option value="KKKNT">KKKNT</option>
				</select>
			  </div>
			  <div style="flex: 2;">
				<label>HTTToán:</label>
				<select id="formHTTToan" class="form-control">
				  <option value="Tiền mặt/Chuyển khoản">Tiền mặt/Chuyển khoản</option>
				  <option value="Tiền mặt">Tiền mặt</option>
				  <option value="Chuyển khoản">Chuyển khoản</option>
				</select>
			  </div>
			  <div style="flex: 1;">
				  <label>Chiết khấu:</label>
				  <input id="formDiscountPerLit" class="form-control" value="0" />
			   </div>
			</div>

			<!-- Email + Biển số xe trên 1 dòng -->
			<div class="d-flex gap-1 mb-1">
			  <div style="flex: 1;">
				<label>Email:</label>
				<input id="formEmail" class="form-control" />
			  </div>
			  <div style="flex: 1;">
				<label>Biển số xe:</label>
				<input id="formPlate" class="form-control" />
			  </div>
			</div>

			<div class="d-flex gap-1 mt-0">
				<button class="btn btn-primary w-100" onclick="updatePreview()">Cập Nhật</button>
				<button class="btn btn-warning w-100" onclick="sendInvoiceFromPopup()">Xuất Hóa Đơn</button>
				<button class="btn btn-secondary w-100" onclick="closePreview()">Đóng</button>
			</div>
		
		</div>

		<!-- Cột phải: Preview hóa đơn -->
		<div style="flex: 1 1 100%;" class="pt-1 pt-md-0 ps-md-1 border-top border-md-top-0 border-md-start">
		  <div id="previewContent"></div>
		</div>

	  </div>
	</div>



	<!-- Popup lý do giao dịch nội bộ -->
	<div id="internalNoteModal" class="modal fade" tabindex="-1">
	  <div class="modal-dialog">
		<div class="modal-content">
		  <div class="modal-header bg-secondary text-white text-center d-block w-100">
			  <h5 class="modal-title" style="font-size: 20px; line-height: 1.4;">
				GIẢI TRÌNH LÝ DO TẠO GIAO DỊCH NỘI BỘ.<br>
				<small style="font-size: 16px; font-style: italic;">⚠ Lưu ý: Giao dịch nội bộ chỉ để sửa máy, lường trụ, kiểm định.</small>
			  </h5>
		  </div>
		  <div class="modal-body">
			<textarea id="internalNoteContent" class="form-control" rows="4" placeholder="Nhập lý do nội bộ..." style="font-size: 16px !important;"></textarea>
		  </div>
		  <div class="modal-footer">
		    <button class="btn btn-danger me-auto" onclick="clearInternalNote()">Xóa</button>
			<button class="btn btn-secondary" onclick="closeInternalNotePopup()">Đóng</button>
			<button class="btn btn-primary" onclick="saveInternalNote()">Lưu</button>
		  </div>
		</div>
	  </div>
	</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script src="{{ url_for('static', filename='invoice_providers/vnpt.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/mobifone_test.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/viettel.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/misa.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/easyinvoice.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/bkav.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/fast.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/hilo.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/mobifone.js') }}"></script>
<script src="{{ url_for('static', filename='main.js') }}"></script>


<script>
  function showModal(id) {
    document.getElementById(id)?.classList.add("show");
  }
  function hideModal(id) {
    document.getElementById(id)?.classList.remove("show");
  }
  function closePreview() {
    document.getElementById("xmlPreview")?.classList.remove("show");
    window.previewData = null;
    loadDefaultCustomerData();
    document.getElementById("previewContent").innerHTML = "";
  }
  // Gán global
  window.showModal = showModal;
  window.hideModal = hideModal;
  window.closePreview = closePreview;
</script>


<script>
  // 👂 Lắng nghe nếu tab khác (setting.html, config_invoice.html, bank_setting.html) vừa lưu
  window.addEventListener("storage", function(e) {
    if (e.key === "settingsUpdatedAt") {
      console.log("🔄 Đã cập nhật cài đặt");

      if (typeof loadDefaultCustomerData === "function") {
        loadDefaultCustomerData();
      }

      Swal.fire({
        icon: "info",
        title: "✅ Đã cập nhật cài đặt",
        text: "Thông tin cài đặt mặc định đã được áp dụng.",
        timer: 2000,
        showConfirmButton: false,
        willClose: () => {
          location.reload();  // F5 khi popup đóng
        }
      });
    }

    if (e.key === "configInvoiceUpdatedAt") {
      console.log("🔄 Đã cập nhật cấu hình hóa đơn");

      Swal.fire({
        icon: "info",
        title: "✅ Đã cập nhật cấu hình hóa đơn",
        text: "Thông tin cấu hình hóa đơn đã được áp dụng.",
        timer: 2000,
        showConfirmButton: false,
        willClose: () => {
          location.reload();  // F5 để lấy lại config mới
        }
      });
    }

    if (e.key === "bankSettingUpdatedAt") {
      console.log("🔄 Đã cập nhật tài khoản ngân hàng");

      Swal.fire({
        icon: "info",
        title: "✅ Đã cập nhật tài khoản ngân hàng",
        text: "Thông tin ngân hàng đã được áp dụng.",
        timer: 2000,
        showConfirmButton: false,
        willClose: () => {
          location.reload();  // F5 để cập nhật QR hoặc các hiển thị liên quan
        }
      });
    }
  });
</script>

<style>
  .swal2-container {
    z-index: 10000 !important;
  }
</style>

</body>
</html>
