<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON>n lý người dùng</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
  <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    body {
      background: #f8f9fa;
      font-family: 'Arial', sans-serif;
    }

    .user-container {
      max-width: 960px;
      margin: 40px auto;
      background: white;
      padding: 30px 40px;
      border-radius: 16px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    label {
      font-weight: bold;
    }

    h3.title {
      color: #dc3545;
      font-weight: bold;
      text-align: center;
      margin-bottom: 32px;
    }

    table td, table th {
      vertical-align: middle !important;
    }
	thead {
		background-color: #063970;
		color: white !important;
	}
	thead th {
		background-color: #063970 !important;
		color: white !important;
	}
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body>

  <div class="user-container">
    <h3 class="title">QUẢN LÝ NGƯỜI DÙNG</h3>
    
	<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

    <!-- Tạo người dùng -->
    <form id="createUserForm" class="mb-4">
      <div class="row g-3">
		  <div class="col-md-4">
			<label for="username">Tên đăng nhập:</label>
			<input type="text" name="username" id="username" class="form-control" required>
		  </div>
		  <div class="col-md-4">
			<label for="password">Mật khẩu:</label>
			<div class="position-relative">
			  <input type="password" name="password" id="password" class="form-control pe-5">
			  <i class="fa fa-eye-slash position-absolute top-50 end-0 translate-middle-y me-3 text-muted"
				 style="cursor: pointer;"
				 onclick="togglePassword(this, 'password')"></i>
			</div>
		  </div>
		  <div class="col-md-4">
			<label for="role">Vai trò:</label>
			<select name="role" id="role" class="form-select">
			  <option value="user">user</option>
			  <option value="admin">admin</option>
			</select>
		  </div>

		  <div class="col-md-4">
			<label for="api_account">API Account:</label>
			<input type="text" id="api_account" name="api_account" class="form-control">
		  </div>
		  <div class="col-md-4">
			<label for="api_pass">API Password:</label>
			<div class="position-relative">
			  <input type="password" name="api_pass" id="api_pass" class="form-control pe-5">
			  <i class="fa fa-eye-slash position-absolute top-50 end-0 translate-middle-y me-3 text-muted"
				 style="cursor: pointer;"
				 onclick="togglePassword(this, 'api_pass')"></i>
			</div>						
		  </div>
		  <div class="col-md-4 d-flex align-items-end">
			<button type="submit" class="btn btn-primary w-100">Thêm</button>
		  </div>
	  </div>
    </form>

	<hr class="my-3" style="border-top: 3px solid #0d6efd;" />
   
	<!-- Đổi mật khẩu -->
	<form id="changePasswordForm" class="mb-4">
      <div class="row g-3 align-items-end">
        <div class="col-md-4">
          <label>Tài khoản cần đổi:</label>
          <input type="text" name="username" class="form-control">
        </div>
        <div class="col-md-4">
			<label>Mật khẩu mới:</label>
			<div class="position-relative">
			  <input type="password" name="new_password" id="new_password" class="form-control pe-5">
			  <i class="fa fa-eye-slash position-absolute top-50 end-0 translate-middle-y me-3 text-muted"
				 style="cursor: pointer;"
				 onclick="togglePassword(this, 'new_password')"></i>
			</div>
        </div>
		<div class="col-md-4">
		  <label class="invisible">.</label> <!-- để giữ thẳng hàng -->
		  <button class="btn btn-warning w-100">Đổi mật khẩu</button>
		</div>
      </div>
    </form>
	
	<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

   	<h5 class="mb-3" style="font-weight: bold;">Danh sách tài khoản</h5>
    
	<!-- Danh sách tài khoản -->   
	<table class="table table-bordered table-hover align-middle text-center">
	  <thead style="background-color: #063970; color: white;">
		<tr>
		  <th>Tên đăng nhập</th>
		  <th>Vai trò</th>
		  <th>API Account</th>
		  <th>Hành động</th>
		</tr>
	  </thead>
	  <tbody>
		{% for u in users %}
		<tr>
		  <td>{{ u.username }}</td>
		  <td>{{ u.role }}</td>
		  <td>{{ u.api_account or '' }}</td>
		  <td>
			{% if u.username != 'admin' %}
			<form class="deleteUserForm" data-username="{{ u.username }}">
			  <button type="submit" class="btn btn-sm btn-danger">Xóa</button>
			</form>
			{% else %}
			<i>Không thể xóa</i>
			{% endif %}
		  </td>
		</tr>
		{% endfor %}
	  </tbody>
	</table>
	
	<p class="mt-1 text-start text-muted" style="font-size: 15px;">
	  🔹 <strong>banhang</strong> là tài khoản dành cho nhân viên tại trụ bơm, chỉ có chức năng tạo mã QR chuyển khoản.
	</p>
	<p class="mt-1 text-start text-muted" style="font-size: 15px;">
	  🔹 <strong>API Account</strong> là tài khoản phụ đi kèm với tài khoản chính, dùng để thay đổi tài khoản hóa đơn khi tạo hóa đơn.
	</p>
  </div>
<script>
function togglePassword(icon, inputId) {
	const input = document.getElementById(inputId);
	const isPassword = input.type === "password";

	input.type = isPassword ? "text" : "password";
	icon.classList.toggle("fa-eye");
	icon.classList.toggle("fa-eye-slash");
}
//-----------------------------------------------------------------------------------------
document.getElementById("createUserForm").addEventListener("submit", async function(e) {
  e.preventDefault();
  const form = e.target;
  const formData = new FormData(form);

  try {
    const res = await fetch("/create_user", {
      method: "POST",
      body: formData
    });

    const json = await res.json();

    Swal.fire({
      icon: json.success ? "success" : "error",
      title: json.success ? "Thành công" : "Lỗi",
      text: json.message || "Đã xử lý.",
      timer: 3000,
      showConfirmButton: false
    });

    if (json.success) {
      setTimeout(() => location.reload(), 1000);
    }
  } catch (err) {
    Swal.fire({
      icon: "error",
      title: "Lỗi mạng",
      text: "Không thể gửi dữ liệu lên server.",
      timer: 3000
    });
  }
});
 
//-----------------------------------------------------------------------------------------
document.querySelectorAll(".deleteUserForm").forEach(form => {
  form.addEventListener("submit", async function(e) {
    e.preventDefault();
    const username = form.dataset.username;

    const confirm = await Swal.fire({
      icon: "warning",
      title: "Xác nhận xóa",
      text: `Bạn có chắc chắn muốn xóa tài khoản "${username}"?`,
      showCancelButton: true,
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy"
    });

    if (!confirm.isConfirmed) return;

    const formData = new FormData();
    formData.append("username", username);

    try {
      const res = await fetch("/delete_user", {
        method: "POST",
        body: formData
      });

      const json = await res.json();

      Swal.fire({
        icon: json.success ? "success" : "error",
        title: json.success ? "Thành công" : "Lỗi",
        text: json.message || "Đã xử lý.",
        timer: 2500,
        showConfirmButton: false
      });

      if (json.success) {
        setTimeout(() => location.reload(), 800);
      }
    } catch (err) {
      Swal.fire("❌ Lỗi", "Không thể kết nối máy chủ.", "error");
    }
  });
});
//-----------------------------------------------------------------------------------------
document.getElementById("changePasswordForm").addEventListener("submit", async function(e) {
  e.preventDefault();
  const form = e.target;
  const formData = new FormData(form);

  try {
    const res = await fetch("/change_password", {
      method: "POST",
      body: formData
    });

    const json = await res.json();

    Swal.fire({
      icon: json.success ? "success" : "error",
      title: json.success ? "Thành công" : "Lỗi",
      text: json.message || "Đã xử lý.",
      timer: 3000,
      showConfirmButton: false
    });

    if (json.success) {
      form.reset();  // Xóa nội dung input
    }
  } catch (err) {
    Swal.fire("❌ Lỗi", "Không thể kết nối máy chủ.", "error");
  }
});
</script>

</body>
</html>
