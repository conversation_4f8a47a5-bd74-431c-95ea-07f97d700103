<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title>Tr<PERSON><PERSON> du<PERSON>ệt debug</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
  <style>
    #debugTable {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      font-size: 14px;
    }

    #debugTable th {
      position: sticky;
      top: 0;
      background-color: #003366 !important;  /* gi<PERSON> nền xanh đậm */
      color: white !important;
      z-index: 5;
      text-align: center;
      font-size: 14px;
      font-weight: bold;
      padding: 6px 10px;
      user-select: none;
    }

    #debugTable td {
      text-align: center;
      vertical-align: middle;
      padding: 6px 10px;
      font-size: 14px;
	  border-bottom: 1px solid #dee2e6;
    }

    #debugTable tr:hover td {
      background-color: #f1f1f1;
    }
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body>
  <div class="container-fluid px-3 flex-grow-1 d-flex flex-column">
    <h3 class="text-center mb-2 text-danger fw-bold">TRÌNH DUYỆT FILE LOG</h3>
    <hr class="my-1" style="border-top: 1px solid #333;" />

	<div class="mb-2 row align-items-center g-2">
	  <!-- Cột 1: Ngày -->
	  <div class="col-lg-3 d-flex align-items-center gap-2">
		<span class="fw-bold text-dark">Ngày:</span>
		<select id="daySelect" class="form-select form-select-sm" style="max-width: 150px; min-width: 50px;" onchange="filterFileList()">
		  <option value="">Tất cả</option>
		</select>
	  </div>

	  <!-- Cột 2: Tìm nhanh (fix rớt dòng) -->
	  <div class="col-lg-3 d-flex align-items-center gap-2" style="min-width: 320px;">
		<span class="fw-bold text-dark mb-0" style="white-space: nowrap;">Tìm nhanh:</span>
		<input type="text" id="searchInput" class="form-control form-control-sm" style="min-width: 0;" placeholder="Nhập tên..." oninput="filterFileList()" />
	  </div>

	  <!-- Cột 3: Tổng dung lượng -->
	  <div class="col-lg-3 text-center fw-bold" id="totalSizeDisplay">
		Tổng dung lượng: 0 KB
	  </div>

	  <!-- Cột 4: Số lượng file -->
	  <div class="col-lg-3 text-end fw-bold" id="totalCountDisplay">
		Số lượng file theo ngày: 0
	  </div>
	</div>



    <div class="table-wrapper">
      <table class="table table-bordered table-hover table-sm mb-0" id="debugTable">
		<thead>
		  <tr>
			<th onclick="sortTable('folder')" style="cursor:pointer;">THƯ MỤC <i class="fa fa-sort"></i></th>
			<th onclick="sortTable('name')" style="cursor:pointer;">TÊN FILE <i class="fa fa-sort"></i></th>
			<th onclick="sortTable('size')" style="cursor:pointer;">KÍCH THƯỚC <i class="fa fa-sort"></i></th>
			<th onclick="sortTable('created_at')" style="cursor:pointer;">THỜI GIAN TẠO <i class="fa fa-sort"></i></th>
			<th>THAO TÁC</th>
		  </tr>
        </thead>
        <tbody id="fileListBody"></tbody>
      </table>
    </div>
  </div>

<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>

<script>
let allFiles = [];

async function loadFileList() {
  const res = await fetch("/api/debug_files");
  const data = await res.json();
  allFiles = data.files;
  initDaySelect();
  filterFileList();
}

function initDaySelect() {
  const daySelect = document.getElementById("daySelect");
  const daySet = new Set(
    allFiles.map(f => f.folder).filter(f => /^[0-9]{8}$/.test(f))
  );
  const days = [...daySet].sort().reverse();

  const today = new Date();
  const yyyy = today.getFullYear();
  const mm = String(today.getMonth() + 1).padStart(2, '0');
  const dd = String(today.getDate()).padStart(2, '0');
  const todayStr = `${yyyy}${mm}${dd}`;

  for (const day of days) {
    const opt = document.createElement("option");
    opt.value = day;
    opt.textContent = day;
    if (day === todayStr) opt.selected = true;  // ✅ chọn mặc định ngày hôm nay
    daySelect.appendChild(opt);
  }
}


function filterFileList() {
  const keyword = document.getElementById("searchInput").value.toLowerCase();
  const selectedDay = document.getElementById("daySelect").value;
  const tbody = document.getElementById("fileListBody");
  const totalDisplay = document.getElementById("totalSizeDisplay");
  const countDisplay = document.getElementById("totalCountDisplay");

  tbody.innerHTML = "";
  let totalKB = 0;
  const folderCountMap = {};

  // Chia file thành 2 nhóm
  const fixedFiles = [];
  const filteredFiles = [];

  for (const file of allFiles) {
    const fullName = `${file.folder}/${file.name}`.toLowerCase();
    const isDateFolder = /^[0-9]{8}$/.test(file.folder);

    if (!fullName.includes(keyword)) continue;

    if (!isDateFolder) {
      fixedFiles.push(file); // luôn hiển thị
    } else {
      if (!selectedDay || file.folder === selectedDay) {
        filteredFiles.push(file);
        folderCountMap[file.folder] = (folderCountMap[file.folder] || 0) + 1;
      }
    }
  }

  // Ghép 2 nhóm lại
  const finalFiles = [...fixedFiles, ...filteredFiles];

  for (const file of finalFiles) {
    const sizeStr = file.size.replace("KB", "").replace(",", "").trim();
    const size = parseFloat(sizeStr) || 0;
    totalKB += size;

    const tr = document.createElement("tr");
    tr.innerHTML = `
	  <td>${file.folder}</td>
	  <td class="file-link text-start">${file.name}</td>
	  <td>${file.size}</td>
	  <td>${file.created_at || ""}</td>
	  <td><a href="/debug_file/${file.folder}/${file.name}" target="_blank" class="btn btn-sm btn-primary">Xem</a></td>
    `;
    tbody.appendChild(tr);
  }

	const totalMB = totalKB / 1024;
	totalDisplay.textContent = `Dung lượng log hóa đơn: ${totalMB.toFixed(2)} MB`;

  const countText = Object.entries(folderCountMap)
    .map(([folder, count]) => `${folder}: ${count}`)
    .join(" | ");

  countDisplay.textContent = countText ? `Số lượng log hóa đơn ngày: ${countText}` : "Số lượng file: 0";
}

let currentSort = { field: "", asc: true };
function sortTable(field) {
  if (currentSort.field === field) {
    currentSort.asc = !currentSort.asc;  // đảo chiều
  } else {
    currentSort.field = field;
    currentSort.asc = true;
  }

  allFiles.sort((a, b) => {
    let valA = a[field];
    let valB = b[field];

    if (field === "size") {
      valA = parseFloat((valA || "").replace("KB", "").replace(",", "")) || 0;
      valB = parseFloat((valB || "").replace("KB", "").replace(",", "")) || 0;
    }

    if (field === "created_at") {
      // Định dạng dd/mm/yyyy hh:mm → yyyy-mm-dd hh:mm
      const toDate = (str) => {
        if (!str) return new Date(0);
        const [d, m, yh] = str.split('/');
        const [y, time] = [yh.slice(0, 4), yh.slice(5)];
        return new Date(`${y}-${m}-${d} ${time}`);
      };
      valA = toDate(valA);
      valB = toDate(valB);
    }

    if (typeof valA === "string") {
      return currentSort.asc ? valA.localeCompare(valB) : valB.localeCompare(valA);
    } else {
      return currentSort.asc ? valA - valB : valB - valA;
    }
  });

  filterFileList();
}

window.onload = async () => {
  await loadFileList();
  filterFileList();

  try {
    const res = await fetch("/api/database_size");
    const json = await res.json();
    if (json.success) {
      const dbKB = json.size_kb;
      const dbMB = (dbKB / 1024).toFixed(1);

      const el = document.createElement("div");
      el.className = "text-center fw-bold text-primary my-2";
      el.textContent = `Tổng dung lượng toàn bộ CSDL: ${dbMB} MB`;
      document.querySelector(".mb-2.row").insertAdjacentElement("afterend", el);
    }
  } catch (err) {
    console.warn("Không thể lấy dung lượng CSDL:", err);
  }
};



</script>
</body>
</html>
