<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login</title>
  <title><PERSON><PERSON>ng nhập</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
  <style>

	body {
	  background: #f8f9fa;
	  font-family: 'Arial', sans-serif;
	  margin: 0;
	  padding: 0;
	}

	.login-container {
	  width: 70%;
	  max-width: 400px;
	 
	  margin: 12vh auto;
	  background: white;
	  padding: 28px;
	  border-radius: 16px;
	  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
	}

	.login-title {
	  text-align: center;
	  font-size: 24px;
	  font-weight: bold;
	  color: #dc3545;
	  margin-bottom: 24px;
	}

	input.form-control {
	  width: 100%;
	  height: 40px;
	  font-size: 18px;
	  margin-bottom: 16px;
	}

	button.btn {
	  width: 100%;
	  height: 52px;
	  font-size: 18px;
	  background-color: #063970;
	  color: white;
	  border: none;
	  border-radius: 8px;
	}

	/* Mobile fine-tune */
	@media (max-width: 480px) {
	  .login-container {
		width: 90%;       /* ✅ Bóp nhỏ từ 90% còn 85% */
		max-width: 85%;   /* ✅ Đảm bảo không nở to lại */
		padding: 24px;
	  }

	  .login-title {
		font-size: 20px;
	  }

	  input.form-control, button.btn {
		height: 40px;
		font-size: 18px;
	  }
	}


  </style>



<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });
</script>

</head>
<body>

  <div class="login-container">

	<div class="login-title">
	  <div style="color: #dc3545;">MONTECH</div>
	  <div style="color: #063970;">BÁN HÀNG TRẠM XĂNG DẦU</div>
	</div>

    {% if error %}
    <div class="alert alert-danger text-center">
      {{ error }}
    </div>
    {% endif %}

    <form method="POST">
      <div class="mb-3">
        <label class="form-label">Tên đăng nhập:</label>
        <input type="text" name="username" class="form-control" placeholder="" />
      </div>

      <div class="mb-3">
        <label class="form-label">Mật khẩu:</label>
        <input type="password" name="password" class="form-control" placeholder=""/> <!-- bỏ chữ required là cho phép để trống -->
      </div>

	  <button type="submit" class="btn btn-primary w-100 mt-3" style="background-color: #063970; border: none;">Đăng nhập</button>
    </form>
  </div>
</body>

<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>

</html>
