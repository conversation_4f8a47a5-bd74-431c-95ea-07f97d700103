// invoice_providers/mobifone.js
async function previewXML_MOBIFONE(code) {
  console.log("📥 [MOBIFONE] G<PERSON>i xem trước hóa đơn cho:", code);
  	// Reset dữ liệu trước khi gọi lại
  if (window.previewData) {
	  window.previewData.discountPerLit = 0;
	  window.previewData.discountTotal = 0;
	  window.previewData.chu = "";
  }
  document.getElementById("formDiscountPerLit").value = "0";

 // Tìm log theo mã giao dịch
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy giao dịch.");

  const invoiceId = log.invoiceId;
  const hasInvoice = isValidInvoiceNumber(log.invoiceNumber);
  
  console.log("🟨 Kiểm tra invoiceNumber:", log.invoiceNumber);
	
  if (isValidInvoiceNumber(log.invoiceNumber)) {
	  const d = new Date(log.transactionDate || new Date());
	  const yyyymmdd = `${d.getFullYear()}${String(d.getMonth() + 1).padStart(2, '0')}${String(d.getDate()).padStart(2, '0')}`;
	  const logUrl = `/debug_file/${yyyymmdd}/${log.invoiceNumber}`;


	  try {
		const res = await fetch(logUrl);
		if (!res.ok) throw new Error("Không thể đọc file log.");

		const data = await res.json();
		const token = data.token;
		const ma_dvcs = data.ma_dvcs || data.response_create?.[0]?.data?.mdvi;
		const hdon_id = data.hdon_id || data.response_create?.[0]?.data?.hdon_id || (Array.isArray(data.response_create?.[0]?.data) ? data.response_create[0].data[0]?.hdon_id : null);

		console.log("🧠 Trích từ file:", { hdon_id, token, ma_dvcs });

		if (!token || !ma_dvcs || !hdon_id) {
		  alert("❌ Thiếu thông tin trong file log");
		  return;
		}
		
		// Lấy từ API pdf_url trong config.json
		const pdfBase = window.currentConfig?.MOBIFONE?.pdf_url || "http://mobiinvoice.vn:9000/api/Invoice68/inHoadon";
		// 🔗 Tạo link tải PDF từ hdon_id
		const pdfUrl = `${pdfBase}?id=${hdon_id}&type=PDF&inchuyendoi=false`;


		const pdfRes = await fetch(pdfUrl, {
		  method: "GET",
		  headers: {
			Authorization: `Bear ${token};${ma_dvcs}`,
		  }
		});

		if (!pdfRes.ok) {
		  const text = await pdfRes.text();
		  console.error("❌ Lỗi tải PDF:", text);
		  alert("Không thể tải file hóa đơn PDF.");
		  return;
		}

		const blob = await pdfRes.blob();
		const blobUrl = URL.createObjectURL(blob);
		window.open(blobUrl, "_blank");
		return;

	  } catch (err) {
		console.error("❌ Lỗi khi tải PDF từ log:", err);
		alert("Không thể mở hóa đơn từ file log.");
		return;
	  }
  }
  
  // Nếu chưa có hóa đơn → Gọi preview
  let rawThue = document.getElementById("formTThue")?.value || "10";
  let thue = 10;
  if (rawThue === "KCT" || rawThue === "KKKNT") {
    thue = 0;
  } else if (rawThue !== null && rawThue !== "") {
    thue = parseFloat(rawThue);
  }

  const customData = {
    ho_ten: document.getElementById("formHoTen")?.value || "",
    ten: document.getElementById("formTen")?.value || "",
    mst: document.getElementById("formMST")?.value || "",
    dchi: document.getElementById("formDChi")?.value || "",
    httt: document.getElementById("formHTTToan")?.value || "",
    thueStr: rawThue,
    thue: thue,
    mkhang: document.getElementById("formMKHang")?.value || "",
    email: document.getElementById("formEmail")?.value || "",
    plate: document.getElementById("formPlate")?.value || "",
    mdvqhns: document.getElementById("formMdvqhns")?.value || "",
	phone: document.getElementById("formPhone")?.value || "",
    cccd: document.getElementById("formCCCD")?.value || "",
    thhdv: document.getElementById("formTHHDVu")?.value || ""
  };

  const res = await fetch('/preview_invoice', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ transactionCode: code, customData })
  });

  const result = await res.json();
  console.log("[MOBIFONE Preview Result]", result);

	if (!result.success || typeof result.json !== "object") {
	  Swal.fire({
		icon: "error",
		title: "❌ Lỗi đơn vị hóa đơn",
		html: result.message || "Kiểm tra lại tài khoản hóa đơn.",
		confirmButtonText: "OK",
		confirmButtonColor: "#6c63ff"
	  });
	  return;
	}

  window.previewData = {
    ...log,
    nl: result.nlap || "",
    sl: Number(result.sl) || 0,
    dgChuaVat: Number(result.dgChuaVat) || 0,
    ttVat: Number(result.ttVat) || 0,
    ttChuaVat: Number(result.ttChuaVat) || 0,
    tienThue: Number(result.tienThue) || 0,
    chu: result.chu || "",
	fuelType: getMappedFuelName((result.fuelType || log?.fuelType || "").trim())
  };

  updatePreview();
  document.getElementById("xmlPreview")?.classList.remove("hidden");
  showModal("xmlPreview");
}

// ======== Gửi từ popup Mobi =========================================
async function sendInvoiceFromPopup_MOBIFONE() {
  const code = window.previewData?.transactionCode;
  if (!code) {
    await Swal.fire({
        icon: "error",
        title: " ❌ Không tìm thấy mã giao dịch.",
		timer: 8000,
        showConfirmButton: true
    });
    return;
  }

  // Nếu là log gộp (999999_xxx) thì gọi hàm riêng
  if (code.startsWith("999999_")) {
    return sendMergedInvoice(); // Đã định nghĩa trong main.js
  }

  // Tiếp tục logic như cũ cho log đơn
  const matched = currentFilteredLogs.find(l => l.transactionCode === code);

  if (matched) {
    const inv = matched.invoiceNumber?.trim() || "";
    const isDaCoHoaDon = inv &&
      inv !== "CHUA_RO_SOHĐ" &&
      inv !== matched.transactionCode &&
      /^[0-9]{4,20}$/.test(inv);

    if (matched.isInvoiced || isDaCoHoaDon) {
      await Swal.fire("⛔ Đã có hóa đơn", `Giao dịch ${code} đã có hóa đơn. Không thể gửi lại.`, "warning");
      return;
    }
  }

  const customData = {
    ho_ten: document.getElementById("formHoTen")?.value || "",
    ten: document.getElementById("formTen")?.value || "",
    mst: document.getElementById("formMST")?.value || "",
    dchi: document.getElementById("formDChi")?.value || "",
    httt: document.getElementById("formHTTToan")?.value || "",
    thue: Number(document.getElementById("formTThue")?.value || 10),
    thueStr: document.getElementById("formTThue")?.value || "10",
    mkhang: document.getElementById("formMKHang")?.value || "",
    email: document.getElementById("formEmail")?.value || "",
    plate: document.getElementById("formPlate")?.value || "",
    mdvqhns: document.getElementById("formMdvqhns")?.value || "",	
	phone: document.getElementById("formPhone")?.value || "",
    cccd: document.getElementById("formCCCD")?.value || "",
    thhdv: document.getElementById("formTHHDVu")?.value || ""
  };

  window.isInvoiceProcessing = true;
  window.isBatchSending = false;
  window.isOwnerOfSingleSend = true;
  window.isOwnerOfBatchSending = false;
   
  await lockInvoiceSend();
  lockScreen();

  try {
    const res = await fetch('/create_invoice', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionCode: code, customData })
    });

    const result = await res.json();
    console.log("📤 Mobifone - Gửi hóa đơn từ popup:", result);

    let shdon = result.shdon || result.invoiceNumber || "";

    if (!shdon) {
      try {
		const responseObj = result.response || {};


        if (Array.isArray(responseObj) && responseObj.length > 0) {
          shdon =
            responseObj[0]?.data?.shdon ||
            responseObj[0]?.data?.so_hoa_don;
        }

        if (!shdon && typeof result.response === "string") {
          const match = result.response.match(/\b\d{4,11}\b/);
          shdon = match ? match[0] : "";
        }
      } catch (e) {
        console.warn("❌ Lỗi parse response Mobifone:", e);
      }
    }

    const isSuccess = result?.success === true;
    const hasHDID = !!result?.hdon_id;

    const msg = shdon
      ? `✅ Số hóa đơn là: <b style="color:green">${shdon}</b>`
      : (isSuccess && hasHDID
          ? "<b style='color:orange'>⏳ Hóa đơn đã tạo, đang chờ ký.</b>"
          : "<b style='color:red'>❌ Không tạo được hóa đơn</b>"
        );

    await Swal.fire({
      icon: (shdon || (isSuccess && hasHDID)) ? "success" : "error",
      title: `Giao dịch ${code}`,
      html: msg,
      timer: 8000,
      showConfirmButton: true
    });


    if (matched) {
      matched.isInvoiced = true;
      matched.invoiceNumber = shdon || "CHUA_RO_SOHĐ";
      matched.verificationCode = matched.invoiceNumber;
    }

    renderTableByPage();
    closePreview();
    loadDefaultCustomerData();
    fetchLogs();

  } catch (err) {
    console.error("❌ Lỗi khi gửi hóa đơn Mobifone:", err);
    await Swal.fire("❌ Lỗi", "Gửi hóa đơn thất bại. Vui lòng thử lại.", "error");
  } finally {
    await unlockInvoiceSend();
    window.isInvoiceProcessing = false;
    unlockScreen();
	//location.reload();
  }
}


// Gán global để gọi từ main.js
window.previewXML_MOBIFONE = previewXML_MOBIFONE;
window.sendInvoiceFromPopup_MOBIFONE = sendInvoiceFromPopup_MOBIFONE;
