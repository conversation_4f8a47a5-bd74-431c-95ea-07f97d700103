// ======== BKAV Invoice Provider =========================================

// Hàm lấy URL PDF từ BKAV API
async function getBkavPdfUrl(transactionCode) {
  try {
    console.log("🔍 [BKAV] Đang lấy URL PDF cho mã giao dịch:", transactionCode);

    // Gọi API backend để lấy URL PDF
    const res = await fetch('/api/bkav_pdf_url', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionCode: transactionCode })
    });

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: ${res.statusText}`);
    }

    const result = await res.json();

    if (result.success && result.pdfUrl) {
      console.log("✅ [BKAV] Lấy URL PDF thành công:", result.pdfUrl);
      return result.pdfUrl;
    } else {
      console.error("❌ [BKAV] Lỗi từ API:", result.message);
      return null;
    }
  } catch (error) {
    console.error("❌ [BKAV] Lỗi khi gọi API lấy PDF:", error);
    return null;
  }
}

// Xem PDF hóa đơn từ log đã có
async function previewXML_BKAV(code) {
  // Tìm log theo mã giao dịch
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy giao dịch.");

  const invoiceId = log.invoiceId;
  const hasInvoice = isValidInvoiceNumber(log.invoiceNumber);
  
  console.log("🟨 Kiểm tra invoiceNumber:", log.invoiceNumber);
	
  if (isValidInvoiceNumber(log.invoiceNumber)) {
	  try {
		// Gọi API để lấy link PDF từ BKAV
		const pdfUrl = await getBkavPdfUrl(log.transactionCode);
		if (pdfUrl) {
		  // Mở PDF trong tab mới
		  window.open(pdfUrl, "_blank");
		  return;
		} else {
		  alert("❌ Không thể lấy link PDF từ BKAV");
		  return;
		}
	  } catch (err) {
		console.error("❌ Lỗi khi lấy PDF từ BKAV:", err);
		alert("Không thể mở hóa đơn. Vui lòng thử lại.");
		return;
	  }
  }

  // Nếu chưa có hóa đơn, gọi preview_invoice để lấy dữ liệu đầy đủ
  console.log("📥 [BKAV] Gọi xem trước hóa đơn cho:", code);

  // Reset dữ liệu trước khi gọi lại
  if (window.previewData) {
    window.previewData.discountPerLit = 0;
    window.previewData.discountTotal = 0;
    window.previewData.chu = "";
  }

  let thueStr = document.getElementById("formTThue")?.value || "10";
  let thue = 10;

  if (thueStr === "KCT" || thueStr === "KKKNT") {
    thue = 0;
  } else if (thueStr !== null && thueStr !== "") {
    thue = parseFloat(thueStr);
  }

  const res = await fetch('/preview_invoice', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      transactionCode: code,
      provider: 'bkav',
      customData: {
        ho_ten: document.getElementById("formHoTen")?.value || "",
        ten: document.getElementById("formTen")?.value || "",
        mst: document.getElementById("formMST")?.value || "",
        dchi: document.getElementById("formDChi")?.value || "",
        htttoan: document.getElementById("formHTTToan")?.value || "",
        thue: thue,
        thueStr: thueStr,
        mkhang: document.getElementById("formMKHang")?.value || "",
        thhdv: getMappedFuelName(log?.fuelType || ""),
        email: document.getElementById("formEmail")?.value || "",
        plate: document.getElementById("formPlate")?.value || "",
        mdvqhns: document.getElementById("formMdvqhns")?.value || "",
        phone: document.getElementById("formPhone")?.value || "",
        cccd: document.getElementById("formCCCD")?.value || ""
      }
    })
  });

  const result = await res.json();
  if (!result.success) {
    alert("Không thể xem trước hóa đơn BKAV");
    return;
  }

  // ✅ Gán dữ liệu đầy đủ từ backend vào previewData
  window.previewData = {
    ...log,
    nl: result.nlap || new Date().toISOString().split('T')[0], // Ngày lập từ backend hoặc ngày hiện tại
    sl: Number(result.sl),
    dgChuaVat: Number(result.dgChuaVat),
    ttVat: Number(result.ttVat),
    ttChuaVat: Number(result.ttChuaVat),
    tienThue: Number(result.tienThue),
    congtienhang: Number(result.ttChuaVat),
    chu: result.chu || "",
    thhdv: result.thhdv || "",
    fuelType: result.fuelType || log.fuelType
  };

  loadDefaultCustomerData();

  document.getElementById("xmlPreview")?.classList.remove("hidden");
  showModal("xmlPreview");
  updatePreview();
}

// Thu thập dữ liệu khách hàng từ form
function collectCustomerData() {
  // Debug: Check if form elements exist
  console.log("🔍 [BKAV DEBUG] Form elements check:");
  console.log("  - formDChi element:", document.getElementById("formDChi"));
  console.log("  - formPhone element:", document.getElementById("formPhone"));
  console.log("  - formPlate element:", document.getElementById("formPlate"));

  const data = {
    mkhang: document.getElementById("formMKHang")?.value || "",    // ✅ Fixed: formMKHang (not formMaKH)
    ho_ten: document.getElementById("formHoTen")?.value || "",
    ten: document.getElementById("formTen")?.value || "",
    mst: document.getElementById("formMST")?.value || "",
    dchi: document.getElementById("formDChi")?.value || "",        // ✅ Fixed: formDChi (not formDiaChi)
    email: document.getElementById("formEmail")?.value || "",
    phone: document.getElementById("formPhone")?.value || "",      // ✅ Fixed: formPhone (not formSDT)
    cccd: document.getElementById("formCCCD")?.value || "",
    mdvqhns: document.getElementById("formMdvqhns")?.value || "",  // ✅ Fixed: formMdvqhns (not formMDVQHNS)
    plate: document.getElementById("formPlate")?.value || "",      // ✅ Fixed: formPlate (not formBienSo)
    htttoan: document.getElementById("formHTTToan")?.value || "Tiền mặt",
    thhdv: document.getElementById("formTHHDVu")?.value || ""      // ⚠️ Note: formTHHDVu not found in HTML
  };

  // Debug: Log collected data
  console.log("🔍 [BKAV DEBUG] Collected customer data:", data);
  console.log("🔍 [BKAV DEBUG] Critical fields:");
  console.log("  - dchi:", data.dchi);
  console.log("  - phone:", data.phone);
  console.log("  - plate:", data.plate);

  return data;
}

// ======== Gửi từ popup Bkav =========================================
async function sendInvoiceFromPopup_BKAV() {
  const code = window.previewData?.transactionCode;
  if (!code) {
    await Swal.fire({
        icon: "error",
        title: " ❌ Không tìm thấy mã giao dịch.",
		timer: 8000,
        showConfirmButton: true
    });
    return;
  }

  // Nếu là log gộp (999999_xxx) thì gọi hàm riêng
  if (code.startsWith("999999_")) {
    return sendMergedInvoice(); // Đã định nghĩa trong main.js
  }

  // Kiểm tra đã xuất hóa đơn chưa
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (log && log.isInvoiced && isValidInvoiceNumber(log.invoiceNumber)) {
    await Swal.fire({
      icon: "warning",
      title: "⚠️ Đã xuất hóa đơn",
      text: `Giao dịch này đã có hóa đơn: ${log.invoiceNumber}`,
      timer: 5000,
      showConfirmButton: true
    });
    return;
  }

  // Khóa để tránh gửi trùng
  if (window.isInvoiceProcessing) {
    await Swal.fire("⚠️ Đang xử lý", "Vui lòng đợi...", "info");
    return;
  }

  window.isInvoiceProcessing = true;
  await lockInvoiceSend();
  lockScreen();

  const customData = collectCustomerData();

  try {
    const res = await fetch('/create_invoice', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionCode: code, customData })
    });

    const result = await res.json();
    console.log("📤 Bkav - Gửi hóa đơn từ popup:", result);

    let invoiceNo = result.invoiceNo || result.invoiceNumber || "";

    if (!invoiceNo) {
      try {
		const responseObj = result.response || {};

        if (Array.isArray(responseObj) && responseObj.length > 0) {
          invoiceNo =
            responseObj[0]?.InvoiceNo ||
            responseObj[0]?.invoiceNo;
        }

        if (!invoiceNo && typeof result.response === "string") {
          const match = result.response.match(/\b\d{4,11}\b/);
          invoiceNo = match ? match[0] : "";
        }
      } catch (e) {
        console.warn("❌ Lỗi parse response Bkav:", e);
      }
    }

    if (result.success) {
      await Swal.fire({
        icon: "success",
        title: "✅ Thành công",
        text: `Hóa đơn đã được tạo${invoiceNo ? `: ${invoiceNo}` : ""}`,
        timer: 3000,
        showConfirmButton: false
      });

      // Reload để cập nhật trạng thái
      await fetchLogs();
      closePreview();
    } else {
      await Swal.fire({
        icon: "error",
        title: "❌ Thất bại",
        text: result.message || "Không thể tạo hóa đơn",
        timer: 8000,
        showConfirmButton: true
      });
    }

  } catch (err) {
    console.error("❌ Lỗi khi gửi hóa đơn Bkav:", err);
    await Swal.fire("❌ Lỗi", "Gửi hóa đơn thất bại. Vui lòng thử lại.", "error");
  } finally {
    await unlockInvoiceSend();
    window.isInvoiceProcessing = false;
    unlockScreen();
  }
}

// Gán global để gọi từ main.js
window.previewXML_BKAV = previewXML_BKAV;
window.sendInvoiceFromPopup_BKAV = sendInvoiceFromPopup_BKAV;
