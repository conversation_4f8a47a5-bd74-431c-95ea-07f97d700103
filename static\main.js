let currentFilteredLogs = [];
let userIsInteracting = false;
let interactionTimeout = null;
let currentPage = 1;
const perPage = 100;
let allLogs = [];              // Chứa toàn bộ log
let isBatchSending = false;  // Biến cờ: đang gửi hóa đơn hàng loạt
let isAutoSending = false; 
let lastChecked = null; // Biến bấm shift checkbox
let currentInternalCode = ""; // Biến GD nội bộ 
let logsPerPage = parseInt(document.getElementById("logsPerPageSelect")?.value) || 100;


// <PERSON><PERSON><PERSON> bảo không bị kẹt lock nếu reload trang
if (window.isOwnerOfBatchSending) {
  fetch("/api/invoice-lock", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ isProcessing: false }),
  });
}


window.isInvoiceProcessing = false;
window.addEventListener("beforeunload", () => {
  navigator.sendBeacon("/api/invoice-lock", JSON.stringify({ isProcessing: false }));
});


window.fuelMap = {};

fetch("/load_config_invoice")
  .then(res => res.json())
  .then(config => {
    window.currentConfig = config;

    const provider = config.provider?.toLowerCase() || "vnpt";

    // Cập nhật dòng providerLabel
    const labelEl = document.getElementById("providerLabel");
    if (labelEl) labelEl.textContent = `(${provider.toUpperCase()})`;

    // Cập nhật trạng thái auto gửi
    updateAutoConfigStatus();

    // Cập nhật tên công ty + MST
    const conf = config[provider.toUpperCase()] || {};
    const nameEl = document.getElementById("sellerName");
    const taxEl = document.getElementById("sellerTaxcode");
    const FullNameEl = document.getElementById("sellerFullName");	

    if (nameEl) nameEl.textContent = conf.sellerName || "";
    if (taxEl) taxEl.textContent = `MST: ${conf.sellerTaxCode || ""} - `;
	if (FullNameEl) FullNameEl.textContent = conf.sellerFullName || "";
  })
  .catch(err => {
    console.error("❌ Lỗi load config:", err);
  });


// ------------------------------------------------------------------------
/*Tải log từ MonBox khi F5 hoặc mở trang
function importLogFromMonboxOnce() {
  const now = Date.now();
  const lastCall = localStorage.getItem("lastImportLogTime");

  if (lastCall && now - parseInt(lastCall) < 30_000) {
    console.log("⏳ Chặn gọi import log vì F5 liên tục (cách chưa đủ 30s)");
    return;
  }

  localStorage.setItem("lastImportLogTime", now);

	fetch("/api/import_log", { method: "POST" })
	  .then(async res => {
		if (!res.ok) {
		  throw new Error(`Máy chủ trả về lỗi ${res.status}`);
		}
		return res.json();
	  })
	  .then(data => {
		console.log("📥 [F5] Tải log MonBox:", data);
		if (!data.success) {
		  Swal.fire({
			icon: "error",
			html: `
			  <div style="font-size: 15px;">
				Lỗi: <b>${data.message || "Không rõ nguyên nhân"}</b><br>
				Vui lòng kiểm tra thiết bị MonBox.
			  </div>
			`,
			timer: 30000
		  });
		}
	  })
	  .catch(err => {
		console.error("❌ Lỗi tải log:", err);
		Swal.fire({
		  icon: "error",
		  html: `
			<div style="font-size: 16px;">
			  Không thể truy cập thiết bị MonBox.<br><br>
			  <b>Vui lòng thực hiện:</b>
			  <ul style="text-align:left; margin-top:5px;">
				<li>Tắt modem mạng và thiết bị MonBox</li>
				<li>Chờ 5 phút → Bật modem, rồi bật MonBox</li>
				<li>Đợi khoảng 3 phút → Tải lại trang (F5)</li>
			  </ul>
			</div>
		  `,
		  confirmButtonText: "OK",
		  confirmButtonColor: "#6c63ff",
		  width: 440,
		  timer: 30000
		});
	  });
} */

// ------------------------------------------------------------------------

function markUserActive() {
  userIsInteracting = true;
  clearTimeout(interactionTimeout);
  interactionTimeout = setTimeout(() => {
    userIsInteracting = false;
    console.log("✅ Không thao tác → cho phép fetchLogs()");
  }, 8000); // reset sau 8 giây
}

// Gắn tất cả sự kiện tương tác
["click", "keydown", "wheel", "scroll", "input", "focusin"].forEach(evt => {
  document.addEventListener(evt, markUserActive, true);
});

let currentSort = { column: null, asc: true };
let autoSendActive = false;
let autoSendInterval = null;
let latestLogCode = "";  // Mã giao dịch mới nhất để so sánh log mới
let lastFetchTime = 0;   // Lưu thời điểm lần fetchLogs() gần nhất


// Load KH từ trang customer
window.allCustomers = [];

fetch("/api/customers")
  .then(res => res.json())
  .then(data => {
    window.allCustomers = data;

    // Khi người dùng bấm Enter trong ô tìm KH
    document.getElementById("searchCustomerPopup").addEventListener("keydown", function (e) {
      if (e.key === "Enter") {
        const rawValue = this.value.trim();
        const input = rawValue.toLowerCase();

        const exists = window.allCustomers.some(c =>
          (c.mkhang || "").toLowerCase().includes(input) ||
          (c.company || "").toLowerCase().includes(input) ||
          (c.tax || "").toLowerCase().includes(input)
        );

        if (!exists && /^[0-9]{10,13}(-\d{3})?$/.test(rawValue)) {
          console.log("Không tìm thấy KH, tìm online với MST:", rawValue);

          // Clear trước
          document.getElementById("formMKHang").value = "";
          document.getElementById("formHoTen").value = "";
          document.getElementById("formEmail").value = "";
          document.getElementById("formPlate").value = "";
          document.getElementById("formCCCD").value = "";		  
          document.getElementById("formMdvqhns").value = "";			  
	  

          document.getElementById("formMST").value = rawValue;

          fetch(`https://api.vietqr.io/v2/business/${rawValue}`)
            .then(res => res.json())
            .then(result => {
              if (result && result.code === '00' && result.data) {
                const { name, address } = result.data;

                document.getElementById("formTen").value = name || "";
                document.getElementById("formDChi").value = address || "";
                document.getElementById("formMST").value = rawValue;
                alert("✅ Tìm thành công! Đã gán thông tin công ty.");

                const newCustomer = {
                  mkhang: "KHONGMA",
                  buyer: "",
                  company: name || "",
                  address: address || "",
                  tax: rawValue,
                  email: "",
                  phone: "",
                  plate: "",
				  mdvqhns: ""				  
                };

                fetch("/api/customers", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(newCustomer)
                })
                .then(res => res.json())
                .then(json => {
                  if (json.success) {
                    console.log("✅ Đã lưu KH MST:", rawValue);
                  } else {
                    console.warn("⚠️ Không thể lưu KH:", json.message || "");
                  }
                });

                updatePreview();
              } else {
                alert("❌ Không tìm thấy thông tin cho MST này.");
              }
            })
            .catch(() => {
              alert("❌ Không thể kết nối để tìm MST.");
            });
        }
      }
    });
  });

function getLocalDateString() {
  const now = new Date();
  now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
  return now.toISOString().split('T')[0];
}



window.addEventListener("DOMContentLoaded", () => {
  // Gán ngày mặc định
  const today = new Date(getLocalDateString());
  document.getElementById('filterDate').value = today.toISOString().slice(0, 10);

  // Gán sự kiện lọc
  document.getElementById("filterDate").onchange = fetchLogs;
  document.getElementById("filterStartTime").onchange = fetchLogs;
  document.getElementById("filterEndTime").onchange = fetchLogs;
  document.getElementById("filterFaucet").onchange = fetchLogs;
  document.getElementById("filterFuel").onchange = fetchLogs;
  document.getElementById("filterPrice").onchange = fetchLogs;
  document.getElementById("filterTank").onchange = fetchLogs;
  document.getElementById("filterCode").oninput = fetchLogs;

  // Gọi các hàm chính
  loadFuelMap();
  loadTankMap();
  //importLogFromMonboxOnce();  // Tải log từ MonBox khi F5 hoặc mở trang
  fetchLogs();
  loadDefaultCustomerData();
  updateAutoConfigStatus();
  
  // Tự động gọi fetchLogs mỗi 10 giây, nhưng chỉ nếu người dùng không thao tác
  setInterval(() => {
	  (async () => {
		const popupVisible = document.getElementById("xmlPreview")?.classList.contains("show");
		const filterInvoiceOnly = document.getElementById("filterHasInvoice")?.checked;
		const filterInternalOnly = document.getElementById("filterInternalOnly")?.checked;
		const filterQRPaidOnly = document.getElementById("filterQRPaidOnly")?.checked;

		console.log("🌀 AutoCheck:", {
		  autoSendActive,
		  userIsInteracting,
		  popupVisible,
		  filterInvoiceOnly
		});

		//if (autoSendActive) {
		//  updateFetchStatus("⏸️ Auto ON");
		//  return;
		//}
		
		if (window.isBatchSending) {
		  updateFetchStatus("⏸️ Đang gởi hóa đơn");
		  return;
		}


		if (userIsInteracting || popupVisible || filterInvoiceOnly || filterInternalOnly) {
		  updateFetchStatus("⏸️ Đang thao tác");
		  return;
		}

		try {
		  const res = await fetch("/api/latest_log_code");
		  const data = await res.json();
		  const now = Date.now();
		  if (data.code && data.code !== latestLogCode && now - lastFetchTime > 3000) {
			  console.log("🆕 Có GD mới:", data.code);
			  fetchLogs();
		  }

		} catch (e) {
		  console.warn("❌ Lỗi khi kiểm tra GD mới:", e);
		}
	  })();
   }, 15000); // Gộp thành 1 lần kiểm tra mỗi 15 giây
});


function formatLit(value) {
  return (value / 1000).toLocaleString('vi-VN', { minimumFractionDigits: 3, maximumFractionDigits: 3 });
}

// Hàm gọi API để load fuel.json từ backend
async function loadFuelMap() {
  try {
    const res = await fetch("/setting/fuel"); // gọi API Flask
    const data = await res.json(); // đọc JSON trả về
    window.fuelMap = data; // gán vào biến toàn cục
    console.log("✅ Đã load fuelMap:", data);
  } catch (e) {
    console.error("❌ Không load được fuelMap:", e);
    window.fuelMap = {};
  }
}
// Hàm gọi API để load tank.json từ backend
async function loadTankMap() {
  try {
    const res = await fetch("/tank_map"); // sử dụng endpoint cho tank (đã định nghĩa trong app.py)
    const data = await res.json();
    window.tankMap = data; // gán vào biến toàn cục
    console.log("✅ Đã load tankMap:", data);
  } catch (e) {
    console.error("❌ Không load được tankMap:", e);
    window.tankMap = {};
  }
}


function getMappedFuelName(code) {
  return window.fuelMap?.[code] || code;
}
window.getMappedFuelName = getMappedFuelName; // để toàn hệ thống dùng được

function getMappedTankName(serial) {
  return (window.tankMap?.[serial] || "").trim();
}
window.getMappedTankName = getMappedTankName; // để toàn hệ thống dùng được


// ======= HÀM fetchLogs() CẬP NHẬT VÀ LOAD LẠI ===========================
async function fetchLogs() {

  updateFetchStatus("🟢 Đang tải GD...");
  updateAutoConfigStatus();
  
  const selectedDate = document.getElementById("filterDate").value;

  const res = await fetch(`/logs?start=${selectedDate}&end=${selectedDate}`);

  const filterNoInvoiceOnly = document.getElementById("filterNoInvoice")?.checked;
  const filterInvoiceOnly = document.getElementById("filterHasInvoice")?.checked;
  const filterInternalOnly = document.getElementById("filterInternalOnly")?.checked;
  const filterQRPaidOnly = document.getElementById("filterQRPaidOnly")?.checked;

  // Cập nhật trạng thái theo checkbox
  if (filterInvoiceOnly) {
	  updateFetchStatus("⏸️ Đang thao tác");
  } else {
	  updateFetchStatus("🟢 Đang tải GD...");
	}
  
  const data = await res.json();
  if (data.logs?.length) {
	latestLogCode = data.logs[0].transactionCode || latestLogCode;
  }
  lastFetchTime = Date.now();  // Cập nhật mốc thời gian fetch

  const startTime = document.getElementById("filterStartTime")?.value;
  const endTime = document.getElementById("filterEndTime")?.value;
  const selectedFuel = document.getElementById("filterFuel").value;
  const selectedFaucet = document.getElementById("filterFaucet").value;
  const selectedPrice = document.getElementById("filterPrice").value;
  const selectedTank = document.getElementById("filterTank").value;  
  const searchCode = document.getElementById("filterCode").value.trim().toLowerCase();

  const fuels = new Set();
  const faucets = new Set();
  const prices = new Set();
  const tanks = new Set();
  
  const dateFiltered = data.logs.filter(log => {
  const matchDate = !selectedDate || log.transactionDate === selectedDate;					  
  const matchTime = (!startTime || log.transactionTime >= startTime) &&(!endTime || log.transactionTime <= endTime);
  return matchDate && matchTime;
  });

	fuels.clear();
	faucets.clear();
	prices.clear();
	tanks.clear();
	
	for (let log of dateFiltered) {
	  const tankName = getMappedTankName(log.pumpSerial);
	  const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
	  const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
	  const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;
	  const matchTank = !selectedTank || tankName === selectedTank;

	  if (matchFaucet && matchPrice && matchTank) fuels.add(log.fuelType);
	  if (matchFuel && matchPrice && matchTank) faucets.add(log.faucetNo);
	  if (matchFuel && matchFaucet && matchTank) prices.add(log.transactionPrice);
	  if (matchFuel && matchFaucet && matchPrice) tanks.add(tankName);
	}

  fillSelect("filterFaucet", faucets);
  fillSelect("filterFuel", fuels);
  fillSelect("filterPrice", prices);
  fillSelect("filterTank", tanks); 
	
  const filtered = dateFiltered.filter(log => {
    const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
    const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
    const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;
	const matchCode =!searchCode ||log.transactionCode.toLowerCase().includes(searchCode) ||log.transactionCost.toString().includes(searchCode);
	const matchNoInvoice = !filterNoInvoiceOnly || (!log.invoiceNumber || (!isValidInvoiceNumber(log.invoiceNumber) && log.invoiceNumber !== "Nội bộ"));
	const matchInvoice =!filterInvoiceOnly ||(log.invoiceNumber &&log.invoiceNumber !== "Nội bộ" && (isValidInvoiceNumber(log.invoiceNumber) || log.invoiceNumber === log.transactionCode));
    const matchInternal = !filterInternalOnly || log.invoiceNumber === "Nội bộ";
	const matchQRPaid = !filterQRPaidOnly || log.qr_paid === true;
	const matchTank = !selectedTank || getMappedTankName(log.pumpSerial) === selectedTank;
	
	return matchFuel && matchFaucet && matchPrice && matchTank && matchCode && matchNoInvoice && matchInvoice && matchInternal && matchQRPaid;
  }); 

	// Sắp xếp log đã lọc
	filtered.sort((a, b) => {
	  if (a.transactionDate !== b.transactionDate)
		return b.transactionDate.localeCompare(a.transactionDate);
	  return b.transactionTime.localeCompare(a.transactionTime);
	});

	// Gán và hiển thị
  allLogs = data.logs;
  currentFilteredLogs = filtered;

  renderPagination();
  renderTableByPage();
  renderSummary(currentFilteredLogs);

  updateFetchStatus("✅ Cập nhật " + new Date().toLocaleTimeString('vi-VN', { hour12: false }));
}

// Hiển thị cập nhật số hóa đơn vào cột hóa đơn
function isValidInvoiceNumber(invoiceNumber) {
  return invoiceNumber && invoiceNumber !== "Nội bộ";
}

function fillSelect(id, values) {
  const select = document.getElementById(id);
  const current = select.value;
  select.innerHTML = '<option value="">Tất cả</option>';

  [...values]
    .sort((a, b) => Number(a) - Number(b))  // 👉 đảm bảo đúng thứ tự số
    .forEach(val => {
      const label =
        id === "filterFuel" ? (window.fuelMap?.[val] || val) :
        id === "filterTank" ? val :  // hoặc custom thêm nếu muốn gắn tên bồn
        val;

      select.innerHTML += `<option value="${val}">${label}</option>`;
    });

  select.value = current;
}



function renderTable(logs) {
  window.allLogs = logs;
  const tbody = document.getElementById('logBody');
  tbody.innerHTML = '';
  const isMobile = window.innerWidth < 768;
 
  for (let log of logs) {
    const row = document.createElement('tr');

    // Lấy đúng invoiceNumber từ DB
    let invoiceStr = String(log.invoiceNumber || '');
    const provider = (log.provider || "").toLowerCase();

    // Cột hóa đơn
    let invoiceHtml = '';
    if (invoiceStr === "Nội bộ") {
      invoiceHtml = `<span class="badge bg-secondary">Nội bộ</span>`;
    } else if (invoiceStr && invoiceStr !== "CHUA_RO_SOHĐ") {
      invoiceHtml = `<span class="badge bg-success rounded-pill px-2 py-1" style="font-size: 12px;">${invoiceStr}</span>`;
    } else {
      invoiceHtml = '<i class="fa-regular fa-file text-danger"></i>';
    }

    // Đổi màu con mắt xem hóa đơn cột thao tác
    let eyeColor = "warning";
    let actionsHtml = "";
    let actionOnClick = `previewLogByCode('${log.transactionCode}')`;

    if (invoiceStr === "Nội bộ") {
      eyeColor = "text-secondary";
      actionOnClick = `showInternalNote('${log.transactionCode}')`;
    } else if (invoiceStr && invoiceStr !== "CHUA_RO_SOHĐ") {
      eyeColor = "text-success"; // chuyển mắt sang màu xanh nếu có invoice
      if ([ "vnpt", "mobifone", "viettel", "misa", "easy", "bkav", "fast", "hilo","mobifone_test",].includes(provider) && log.invoiceId) {
        actionOnClick = `previewLogByCode('${log.transactionCode}')`; 
      }
    }

    // Đổi màu hình QR cột thao tác
    const qrColor = log.qr_paid ? "text-success" : "text-danger";

	actionsHtml = `
	  <button onclick="${actionOnClick}" title="Xem chi tiết">
		<i class="fa fa-eye ${eyeColor}"></i>
	  </button>
	  
	  <button onclick="generateQR('${log.transactionCode}')" title="Tạo mã QR" style="margin-left: 4px;">
		<i id="qr-icon-${log.transactionCode}" class="fa-solid fa-qrcode ${qrColor}"></i>
	  </button>`;

	if (isMobile) {
      row.innerHTML = `
        <td><input type="checkbox" class="row-checkbox" value="${log.transactionCode}" ${(log.isInvoiced || invoiceStr === "Nội bộ") ? 'disabled' : ''} /></td>
        <td>${log.transactionCost.toLocaleString('vi-VN')}</td>
        <td>${formatLit(log.transactionAmount)}</td>
        <td>${log.transactionPrice.toLocaleString('vi-VN')}</td>
        <td>${actionsHtml}</td>   
        <td>${getMappedFuelName(log.fuelType)}</td>		
		<td>${invoiceHtml}</td>
		<td>${log.transactionDate.split("-").reverse().join("-")}</td>
        <td>${log.transactionTime}</td>
        <td>${log.faucetNo}</td>
        <td>${log.pumpSerial}</td>
		<td>${getMappedTankName(log.pumpSerial)}</td>
        <td>${log.transactionCode}</td>
        <td>${log.stationCode || ''}</td>
      `;
    tbody.appendChild(row);    
	} else {
      row.innerHTML = `
        <td><input type="checkbox" class="row-checkbox" value="${log.transactionCode}" ${(log.isInvoiced || invoiceStr === "Nội bộ") ? 'disabled' : ''} /></td>
		<td>${log.transactionDate.split("-").reverse().join("-")}</td>
        <td>${log.transactionTime}</td>
        <td>${log.faucetNo}</td>
        <td>${getMappedFuelName(log.fuelType)}</td>
        <td>${log.transactionCost.toLocaleString('vi-VN')}</td>
        <td>${formatLit(log.transactionAmount)}</td>
        <td>${log.transactionPrice.toLocaleString('vi-VN')}</td>
        <td>${invoiceHtml}</td>
        <td>${actionsHtml}</td>
        <td>${log.pumpSerial}</td>
		<td>${getMappedTankName(log.pumpSerial)}</td>
        <td>${log.transactionCode}</td>
        <td>${log.stationCode || ''}</td>
      `;
    tbody.appendChild(row);
	}
  }
}



function renderSummary(logs) {
  const isMobile = /Android|iPhone|iPod|Mobile|Windows Phone/i.test(navigator.userAgent);


  let totalAmount = 0;
  let totalVolume = 0;

  for (let log of logs) {
    totalAmount += log.transactionCost;
    totalVolume += log.transactionAmount / 1000;
  }

	const countInvoiced = logs.filter(log =>
	  (isValidInvoiceNumber(log.invoiceNumber) || log.invoiceNumber === log.transactionCode) &&
	  log.invoiceNumber !== "Nội bộ"
	).length;

  const countInternal = logs.filter(log => log.invoiceNumber === "Nội bộ").length;
  const countChuaTao = logs.length - countInvoiced - countInternal;

  
  const countPaidQR = logs.filter(log => log.qr_paid).length;

  const wasNoInvoiceChecked = document.getElementById("filterNoInvoice")?.checked;
  const wasInvoiceChecked = document.getElementById("filterHasInvoice")?.checked;
  const wasInternalChecked = document.getElementById("filterInternalOnly")?.checked;
  const wasQRPaidChecked = document.getElementById("filterQRPaidOnly")?.checked;
  
  

  let summaryHTML = '';

  if (isMobile) {
	// Mobile
	summaryHTML = `
	  <div class="d-grid gap-1 small text-center" style="line-height: 1.6;">
		<div style="white-space: nowrap;">
		  Tổng tiền: <b class="stat-number text-primary">${totalAmount.toLocaleString('vi-VN')}</b>&nbsp; |
		  Tổng Lít: <b class="stat-number text-primary">${totalVolume.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</b>&nbsp; |
		  Tổng GD: <b class="stat-number text-primary">${logs.length}</b>
		</div>
		<div style="white-space: nowrap;">
		  <input type="checkbox" id="filterNoInvoice" onchange="onToggleNoInvoice()" />Ch.xuất: <b class="stat-number text-danger">${countChuaTao}</b> |
		  <input type="checkbox" id="filterHasInvoice" onchange="onToggleInvoice()" />H.đơn: <b class="stat-number text-success">${countInvoiced}</b> |
		  <input type="checkbox" id="filterInternalOnly" onchange="onToggleInternal()" />N.bộ: <b class="stat-number text-secondary">${countInternal}</b> |
		  <input type="checkbox" id="filterQRPaidOnly" onchange="onToggleQRPaid()" />CK: <b class="stat-number text-success">${countPaidQR}</b> |
		  Chọn: <b id="selectedCountValue" class="stat-number text-primary">0</b>
		</div>
		<div id="autoSummary">
	  </div>
	`;


  } else {
    // Desktop: đầy đủ
    summaryHTML = `
      <div style="display: flex; flex-wrap: wrap; gap: 24px; align-items: center;">
        <div><span class="label">Đã chọn:</span> <span class="stat-number text-primary" id="selectedCountValue">0</span> GD</div>
        <div><span class="label">Tổng tiền:</span> <span class="stat-number text-primary">${totalAmount.toLocaleString('vi-VN')}</span> đồng</div>
        <div><span class="label">Tổng lít:</span> <span class="stat-number text-primary">${totalVolume.toLocaleString('vi-VN', { minimumFractionDigits: 3, maximumFractionDigits: 3 })}</span> lít</div>
        <div><span class="label">Tổng giao dịch:</span> <span class="stat-number text-primary">${logs.length}</span></div>

		<label class="form-check d-flex align-items-center gap-1">
		  <input type="checkbox" id="filterNoInvoice" onchange="onToggleNoInvoice()" />
		  <span>Chưa xuất: <b class="stat-number text-danger">${countChuaTao}</b></span>
		</label>
		
		<label class="form-check d-flex align-items-center gap-1">
          <input type="checkbox" id="filterHasInvoice" onchange="onToggleInvoice()" />
          <span>Đã xuất: <b class="stat-number text-success">${countInvoiced}</b></span>
        </label>

        <label class="form-check d-flex align-items-center gap-1">
          <input type="checkbox" id="filterInternalOnly" onchange="onToggleInternal()" />
          <span>Đã tạo nội bộ: <b class="stat-number text-secondary">${countInternal}</b></span>
        </label>
		
		<label class="form-check d-flex align-items-center gap-1">
		  <input type="checkbox" id="filterQRPaidOnly" onchange="onToggleQRPaid()" />
		  <span>Đã CK: <b class="stat-number text-success">${countPaidQR}</b></span>
		</label>

        <div id="autoSummary">
          <span class="label">Đã xuất:</span>
          <span class="stat-number text-secondary"><span id="sentCount">0</span>/<span id="totalCount">0</span></span>
          <span style="color: black;"> GD</span>
        </div>
      </div>
    `;
  }

  document.getElementById("logSummary").innerHTML = summaryHTML;
  if (wasNoInvoiceChecked) document.getElementById("filterNoInvoice").checked = true;
  if (wasInvoiceChecked) document.getElementById("filterHasInvoice").checked = true;
  if (wasInternalChecked) document.getElementById("filterInternalOnly").checked = true;
  if (wasQRPaidChecked) document.getElementById("filterQRPaidOnly").checked = true;
}


// ==== Xử lý tick vào ô lọc Chưa tạo hóa đơn ===========
function onToggleNoInvoice() {
  const invoiceCheckbox = document.getElementById("filterHasInvoice");
  const internalCheckbox = document.getElementById("filterInternalOnly");
  const noInvoiceCheckbox = document.getElementById("filterNoInvoice");

  if (noInvoiceCheckbox.checked) {
    invoiceCheckbox.checked = false;
    internalCheckbox.checked = false;
  }

  fetchLogs();
}


// ==== Xử lý tick vào ô lọc Đã tạo hóa đơn ===========
function onToggleInvoice() {
  const invoiceCheckbox = document.getElementById("filterHasInvoice");
  const internalCheckbox = document.getElementById("filterInternalOnly");
  const noInvoiceCheckbox = document.getElementById("filterNoInvoice");

  if (invoiceCheckbox.checked) {
    internalCheckbox.checked = false;
    noInvoiceCheckbox.checked = false;
  }

  fetchLogs();
}

// ==== Xử lý tick vào ô lọc Đã tạo nội bộ ===========
function onToggleInternal() {
  const invoiceCheckbox = document.getElementById("filterHasInvoice");
  const internalCheckbox = document.getElementById("filterInternalOnly");
  const noInvoiceCheckbox = document.getElementById("filterNoInvoice");

  if (internalCheckbox.checked) {
    invoiceCheckbox.checked = false;
    noInvoiceCheckbox.checked = false;
  }

  fetchLogs();
}


// ==== Xử lý tick vào ô lọc Đã CK QR ===========
function onToggleQRPaid() {
  const qrCheckbox = document.getElementById("filterQRPaidOnly");
  fetchLogs(); // load lại log có lọc
}



// Đếm số lượng đang checkbox
function updateSelectedCheckboxCount() {
  const count = document.querySelectorAll('.row-checkbox:checked').length;
  const span = document.getElementById("selectedCountValue");
  if (span) {
    span.textContent = count > 0 ? count.toLocaleString("vi-VN") : "0";
  }
}

// Gọi khi checkbox thay đổi
document.addEventListener("click", function (e) {
  if (e.target && e.target.classList.contains("row-checkbox")) {
    const checkboxes = [...document.querySelectorAll('.row-checkbox:not(:disabled)')];

    if (e.shiftKey && lastChecked) {
      const start = checkboxes.indexOf(lastChecked);
      const end = checkboxes.indexOf(e.target);
      const [min, max] = [Math.min(start, end), Math.max(start, end)];

      for (let i = min; i <= max; i++) {
        checkboxes[i].checked = true;
      }
    }

    lastChecked = e.target;
    updateSelectedCheckboxCount();
  }
});

// Gọi checkbox khi render lại bảng
window.updateSelectedCheckboxCount = updateSelectedCheckboxCount;

// Bấm chọn tất cả checkbox
function toggleSelectAll(masterCheckbox) {
  const checkboxes = document.querySelectorAll('.row-checkbox');
  checkboxes.forEach(cb => {
    if (!cb.disabled) {
      cb.checked = masterCheckbox.checked;
    }
  });
  updateSelectedCheckboxCount();
}

// Tìm thông minh trong popup, lấy KH từ customer
function searchCustomerPopup() {
  const keyword = document.getElementById("searchCustomerPopup").value.toLowerCase();
  const datalist = document.getElementById("customerList");
  datalist.innerHTML = "";

  const matched = window.allCustomers.filter(c =>
    (c.mkhang || "").toLowerCase().includes(keyword) ||
    (c.company || "").toLowerCase().includes(keyword) ||
    (c.tax || "").toLowerCase().includes(keyword)
  );

  matched.forEach(c => {
    const opt = document.createElement("option");
    opt.value = `${c.mkhang || "(KHONGMA)"} - ${c.company} - ${c.tax}`;
    opt.dataset.customer = JSON.stringify(c);
    datalist.appendChild(opt);
  });
}


// Tìm thông minh trong popup, lấy KH từ customer, gán vào các input bên dưới nếu tìm thấy
function autoFillCustomerByInput() {
  const rawInput = document.getElementById("searchCustomerPopup").value.trim();
  const input = rawInput.toLowerCase();

  const found = window.allCustomers.find(c => {
    const value = `${(c.mkhang || "(KHONGMA)").toLowerCase()} - ${(c.company || "").toLowerCase()} - ${(c.tax || "").toLowerCase()}`;
    return input === value || value.includes(input);
  });

  if (!found) {
    console.log("❌ Không tìm thấy khách hàng phù hợp");

    // Không tự gọi searchMST tại đây nữa
    return;
  }

  // Gán thông tin KH nếu tìm thấy
  document.getElementById("formMKHang").value = found.mkhang || "";
  document.getElementById("formHoTen").value = found.buyer || "";
  document.getElementById("formTen").value = found.company || "";
  document.getElementById("formMST").value = found.tax || "";
  document.getElementById("formDChi").value = found.address || "";
  document.getElementById("formHTTToan").value = found.payment || "Tiền mặt/Chuyển khoản";
	if (found.vat !== undefined && found.vat !== null && found.vat !== "") {
	  document.getElementById("formTThue").value = found.vat;
	} else if (window.defaultCustomerTThue) {
	  document.getElementById("formTThue").value = window.defaultCustomerTThue;
	}
  document.getElementById("formEmail").value = found.email || "";
  document.getElementById("formPlate").value = found.plate || "";
  document.getElementById("formMdvqhns").value = found.mdvqhns || "";
  document.getElementById("formPhone").value = found.phone || "";
  updatePreview()
}


//=========== XEM TRƯỚC POPUP==========
async function previewXML(code) {
  await loadFuelMap(); // Đảm bảo fuelMap luôn mới
  const provider = (window.currentConfig?.provider || localStorage.getItem("currentProvider") || "vnpt").toLowerCase();
  const handler = InvoiceProviderHandlers[provider];
  if (handler?.preview) return handler.preview(code);
  alert(`❌ Không hỗ trợ xem hóa đơn cho nhà cung cấp: ${provider}`);
}

//=========== CẬP NHẬT POPUP XEM TRƯỚC HÓA ĐƠN GTGT==========
async function updatePreview() {
  const d = window.previewData || {};
  const provider = (window.currentConfig?.provider || "vnpt").toLowerCase();
  const conf = window.currentConfig?.[provider.toUpperCase()] || {};

  let thueStr = document.getElementById("formTThue").value || "10";
  let thue = (thueStr === "KCT" || thueStr === "KKKNT") ? 0 : parseFloat(thueStr);

  // Tạo bản sao mới chứ không ghi vào window.previewData
  const tempData = {
    ...d,
    ho_ten: document.getElementById("formHoTen").value,
    ten: document.getElementById("formTen").value,
    mst: document.getElementById("formMST").value,
    dchi: document.getElementById("formDChi").value,
    httt: document.getElementById("formHTTToan").value,
	thue,       // dùng để tính toán
	thueStr,    // dùng để hiển thị KKKNT
    mkhang: document.getElementById("formMKHang").value,
    thhdv: getMappedFuelName(d.fuelType || ""),
	email: document.getElementById("formEmail").value,
	plate: document.getElementById("formPlate").value,	
	phone: document.getElementById("formPhone").value,
	cccd: document.getElementById("formCCCD").value,
	mdvqhns: document.getElementById("formMdvqhns").value	
  };

  // TÍNH LẠI SỐ LIỆU GIỐNG BACKEND
	const priceVat = d.transactionPrice || 0;
	const costVat = d.transactionCost || 0;
	const sl = +(d.transactionAmount || 0) / 1000;


	// Tính chiết khấu thương mại
	const discountPerLitVat = parseFloat(document.getElementById("formDiscountPerLit")?.value || "0") || 0;

	let dgChuaVat = 0;
	let ttChuaVat = 0;
	let tienThue = 0;
	let tongGoc = 0;
	let discountPerLit = 0;
	let discountTotal = 0;
	let congtienhang =0;

	if (discountPerLitVat > 0) {
	  // Nếu có chiết khấu: chuyển về chưa VAT, tính đúng phần giảm
	  discountPerLit = +(discountPerLitVat / (1 + thue / 100)).toFixed(4);
	  discountTotal = +(sl * discountPerLit).toFixed(0);
	  
	  dgChuaVat = +(priceVat / (1 + thue / 100)).toFixed(4);
	  ttChuaVat = +(costVat / (1 + thue / 100)).toFixed(0);
	  
	  congtienhang = ttChuaVat - discountTotal;
	  tienThue = +(congtienhang * thue / 100).toFixed(0);
	  tongGoc = congtienhang + tienThue;

	} else {
	  // Nếu không có chiết khấu: giữ nguyên công thức gốc
	  dgChuaVat = +(priceVat / (1 + thue / 100)).toFixed(4);
	  ttChuaVat = +(costVat / (1 + thue / 100)).toFixed(0);
	  congtienhang = ttChuaVat;
	  tongGoc = costVat;
	  tienThue = +(costVat - ttChuaVat).toFixed(0);
	}

	// Gán vào tempData để render
	tempData.sl = sl;
	tempData.dgChuaVat = dgChuaVat;
	tempData.ttChuaVat = ttChuaVat;
	tempData.tienThue = tienThue;
	tempData.ttVat = tongGoc;

	tempData.discountPerLit = discountPerLit;
	tempData.discountPerLitVat = discountPerLitVat;
	tempData.discountTotal = discountTotal;
	try {
	  const res = await fetch("/api/num2words", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify({ amount: Math.round(tongGoc) })
	  });
	  const data = await res.json();
	  tempData.chu = data.chu || (Math.round(tongGoc) + " đồng chẵn.");
	} catch (e) {
	  tempData.chu = Math.round(tongGoc) + " đồng chẵn.";
	}


  const isMobile = /iPhone|Android|Mobile/i.test(navigator.userAgent);
  const hienChiTietCK = discountTotal > 0;

  // HTML chiết khấu riêng (fix lỗi xuống dòng trong template literal)
  let ckHTML = "";
  if (hienChiTietCK) {
    ckHTML = `
      <div><strong>Chiết khấu thương mại:</strong></div>
      <div style="text-align: right; color: #063970;">-${discountTotal.toLocaleString('vi-VN')} đ</div>
    `;
  }

  let html = "";

  if (isMobile) {
    html = `
      <div class="preview-header" style="text-align: center; margin-bottom: 1px;">
        <h2 style="color: #d32f2f; font-weight: bold; font-size: 16px;">THÔNG TIN XUẤT HÓA ĐƠN</h2>
        <div style="font-style: italic; font-size: 15px;">
          Ngày: ${
            (() => {
              const [y, m, d] = (tempData.nl || "").split("-");
              return `${d}-${m}-${y}`;
            })()
          }
        </div>
      </div>

      <div class="preview-body" style="font-size: 15px; line-height: 1.5;">
        <div style="margin-bottom: 2px;"><strong>Mã khách hàng:</strong> <span style="color: #063970;">${tempData.mkhang}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <strong>Biển số xe:</strong> <span style="color: #063970;">${tempData.plate}</span></div>
        <div style="margin-bottom: 2px;"><strong>Họ tên người mua:</strong> <span style="color: #063970;">${tempData.ho_ten}</span></div>
		<div style="margin-bottom: 2px;"><strong>CCCD:</strong> <span style="color: #063970;">${tempData.cccd}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <strong>SĐT:</strong> <span style="color: #063970;">${tempData.phone}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <strong>Mã ĐVQHNS:</strong> <span style="color: #063970;">${tempData.mdvqhns}</div>
        <div style="margin-bottom: 2px;"><strong>Tên đơn vị mua:</strong> <span style="color: #063970;">${tempData.ten}</span></div>
        <div style="margin-bottom: 2px;"><strong>Mã số thuế:</strong> <span style="color: #063970;">${tempData.mst}</span></div>
        <div style="margin-bottom: 2px;"><strong>Địa chỉ:</strong> <span style="color: #063970;">${tempData.dchi}</span></div>
        <div style="margin-bottom: 2px;"><strong>Email:</strong> <span style="color: #063970;">${tempData.email}</span></div>
        <div style="margin-bottom: 2px;"><strong>Hình thức thanh toán:</strong> <span style="color: #063970;">${tempData.httt}</span></div>
      </div>

      <hr style="margin: 1px 0; border-top: 2px solid #063970;" />

      <div class="invoice-details" style="font-size: 15px; display: grid; grid-template-columns: 2fr 1fr; gap: 4px;">
        <div><strong>Tên hàng hóa:</strong></div><div style="text-align: right; color: #063970;">${tempData.thhdv}</div>
        <div><strong>Đơn vị tính:</strong></div><div style="text-align: right; color: #063970;">Lít</div>
        <div><strong>Số lượng:</strong></div><div style="text-align: right; color: #063970;">${sl.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</div>
        <div><strong>Đơn giá:</strong></div><div style="text-align: right; color: #063970;">${dgChuaVat.toLocaleString('vi-VN', { minimumFractionDigits: 4 })}</div>
        <div><strong>Thành tiền:</strong></div><div style="text-align: right; color: #063970;">${ttChuaVat.toLocaleString('vi-VN')}</div>

        ${ckHTML}

        <div style="grid-column: 1 / span 2;"><hr style="margin: 1px 0; border-top: 2px solid #063970;"/></div>

        <div><strong>Cộng tiền hàng:</strong></div><div style="text-align: right; color: #063970;">${congtienhang.toLocaleString('vi-VN')}</div>
        <div><strong>Thuế suất GTGT ${tempData.thueStr}%:</strong></div><div style="text-align: right; color: #063970;">${tienThue.toLocaleString('vi-VN')}</div>
        <div><strong style="font-size: 15px;">Tổng cộng thanh toán:</strong></div><div style="text-align: right; font-weight: bold; font-size: 16px; color: #063970;">${tongGoc.toLocaleString('vi-VN')}</div>

        <div style="grid-column: 1 / span 2;"><hr style="margin: 1px 0; border-top: 2px solid #063970;"/></div>
		
        <div style="grid-column: 1 / span 2;"><strong>Số tiền viết bằng chữ:</strong> <em style="color: #063970;">${tempData.chu}</em></div>
      </div>
    `;
	 } else {
	  const hienChiTietCK = discountTotal > 0;

	  html = `
	  <div class="preview-header" style="text-align: center; margin-bottom: 10px;">
		<h2 style="color: #d32f2f; margin-bottom: 2px; font-size: 18px;">THÔNG TIN XUẤT HÓA ĐƠN</h2>
		<div style="font-style: italic;">
		  Ngày: ${
			(() => {
			  const [y, m, d] = (tempData.nl || "").split("-");
			  return `${d}-${m}-${y}`;
			})()
		  }
		</div>
		${tempData.gopFromCodes ? `<div style="font-style: italic; color: #0d6efd; margin: 4px 0 6px;">
		   Đã gộp từ ${tempData.gopFromCodes.length} giao dịch
		</div>` : ""}
	  </div>
	  
	  <hr style="margin: 1px 0; border-top: 3px solid #0d6efd;" />

	  <div class="preview-body" style="font-size: 15px; line-height: 1.5;">
		<div style="margin-bottom: 4px;"><strong>Họ tên người bán:</strong> ${conf.sellerFullName || ""}</div>
		<div style="margin-bottom: 4px;"><strong>Tên đơn vị bán:</strong> ${conf.sellerName || ""}</div>
		<div style="margin-bottom: 4px;"><strong>Mã số thuế bên bán:</strong> ${conf.sellerTaxCode || ""}</div>
		<div style="margin-bottom: 4px;"><strong>Địa chỉ bên bán:</strong> ${conf.sellerAddress || ""}</div>
	  </div>

	  <hr style="margin: 1px 0; border-top: 3px solid #0d6efd;" />

	  <div class="preview-body" style="font-size: 15px; line-height: 1.5;">
		<div style="margin-bottom: 4px;">
		  <strong>Mã khách hàng:</strong><span style="color: #063970;"> ${tempData.mkhang}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		  <strong>Biển số xe:</strong><span style="color: #063970;"> ${tempData.plate}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		  <strong>Mã ĐVQHNS:</strong><span style="color: #063970;"> ${tempData.mdvqhns}</span>		  
		</div>
		
	    <div style="margin-bottom: 4px;">
		  <strong>Họ tên người mua:</strong><span style="color: #063970;"> ${tempData.ho_ten}</span>
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  <strong>CCCD:</strong><span style="color: #063970;"> ${tempData.cccd}</span>
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  <strong>SĐT:</strong><span style="color: #063970;"> ${tempData.phone}</span>
		</div>

		<div style="margin-bottom: 4px;">
		  <strong>Tên đơn vị mua:</strong><span style="color: #063970;"> ${tempData.ten}</span>
		</div>
		
		<div style="margin-bottom: 4px;">
		  <strong>Mã số thuế bên mua:</strong><span style="color: #063970;"> ${tempData.mst}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		  <strong>Email:</strong><span style="color: #063970;"> ${tempData.email}</span>
		</div>
		
		<div style="margin-bottom: 4px;">
		  <strong>Địa chỉ bên mua:</strong><span style="color: #063970;"> ${tempData.dchi}</span>
		</div>
		
		<div style="margin-bottom: 4px;">
		  <strong>Hình thức thanh toán:</strong><span style="color: #063970;"> ${tempData.httt}</span>
		</div>
	  </div>

	  <table class="table table-bordered table-sm mt-2 invoice-table">
		<thead class="invoice-header text-center">
		  <tr>
			<th>STT</th>
			<th>Tên hàng hóa, dịch vụ</th>
			<th>ĐVT</th>
			<th>Số lượng</th>
			<th>Đơn giá</th>
			<th>Thành tiền</th>
		  </tr>
		</thead>
		<tbody>
		  <tr>
			<td class="text-center">1</td>
			<td>${tempData.thhdv}</td>
			<td class="text-center">Lít</td>
			<td class="text-end">${sl.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</td>
			<td class="text-end">${dgChuaVat.toLocaleString('vi-VN', { minimumFractionDigits: 4 })}</td>
			<td class="text-end">${ttChuaVat.toLocaleString('vi-VN')}</td>
		  </tr>

		  ${hienChiTietCK ? `
		  <tr>
			<td class="text-center">2</td>
			<td>Chiết khấu thương mại</td>
			<td class="text-center">Lít</td>
			<td class="text-end">${sl.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</td>
			<td class="text-end">${discountPerLit.toLocaleString('vi-VN', { minimumFractionDigits: 4 })}</td>
			<td class="text-end">${discountTotal.toLocaleString('vi-VN')}</td>
		  </tr>` : ""}

		  <tr>
			<td colspan="5" class="text-end"><strong>Cộng tiền hàng:</strong></td>
			<td class="text-end">${congtienhang.toLocaleString('vi-VN')}</td>
		  </tr>
		  <tr>
			<td colspan="5" class="text-end"><strong>Thuế suất GTGT(%):</strong> ${tempData.thueStr}</td>
			<td class="text-end">${tienThue.toLocaleString('vi-VN')}</td>
		  </tr>
		  <tr>
			<td colspan="5" class="text-end"><strong>Tổng cộng tiền thanh toán:</strong></td>
			<td class="text-end"><strong>${tongGoc.toLocaleString('vi-VN')}</strong></td>
		  </tr>
		</tbody>
	  </table>

	  <div style="margin-top: 8px;"><strong>Số tiền viết bằng chữ:</strong> <em>${tempData.chu}</em></div>
	</div>
	`;
	}

  document.getElementById("previewContent").innerHTML = html;
}


//=========== ĐÓNG POPUP PREVIEW ==========
function closePreview() {
  document.getElementById('xmlPreview')?.classList.remove('show');

  // Xóa preview hiện tại
  window.previewData = null;

  // Reset lại form người mua về mặc định
  loadDefaultCustomerData();

  // (Tuỳ chọn) Xoá nội dung XML preview nếu cần
  document.getElementById("previewContent").innerHTML = "";
}


//=========== BẤM NÚT GỞI HÓA ĐƠN TRONG POPUP PREVIEW ==========
function sendInvoiceFromPopup() {
  // ✅ FIX: Use provider from the specific log/invoice, not global setting
  // This ensures that Viettel invoices use Viettel send function, BKAV invoices use BKAV, etc.
  let provider;

  if (window.previewData && window.previewData.transactionCode) {
    const log = allLogs.find(l => l.transactionCode === window.previewData.transactionCode);
    if (log && log.provider && log.provider.trim() !== "") {
      // Use the provider that was used to create this specific invoice
      provider = log.provider.toLowerCase();
      console.log(`🔍 [SEND] Using log-specific provider: ${provider} for transaction: ${window.previewData.transactionCode}`);
    } else {
      // Fallback to global provider setting for logs without provider info
      provider = (window.currentConfig?.provider || localStorage.getItem("currentProvider") || "vnpt").toLowerCase();
      console.log(`🔍 [SEND] Using global provider: ${provider} for transaction: ${window.previewData.transactionCode}`);
    }
  } else {
    // Fallback to global provider if no preview data
    provider = (window.currentConfig?.provider || localStorage.getItem("currentProvider") || "vnpt").toLowerCase();
    console.log(`🔍 [SEND] Using global provider (no preview data): ${provider}`);
  }

  const handler = InvoiceProviderHandlers[provider];
  if (handler?.send) {
    return handler.send();  // gọi hàm gửi của nhà cung cấp
  }
  alert(`❌ Không hỗ trợ gửi hóa đơn cho nhà cung cấp: ${provider}`);
}


//=========== BẤM NÚT XUẤT HÓA ĐƠN KHI CHECKBOX sendBatchInvoices ==========
async function sendBatchInvoices() {
  if (!window.isAdmin) {
    Swal.fire("⛔ Không hỗ trợ", "Xuất hàng tự động loạt chỉ có tại Admin.", "warning");
    return;
  }

  const selected = Array.from(document.querySelectorAll('.row-checkbox:checked'));
  const codes = selected.map(x => x.value);
  console.log("[DEBUG] ✅ Gọi sendBatchInvoices với", codes);

  if (!selected.length) {
    Swal.fire("⚠️", "Vui lòng chọn giao dịch.", "warning");
    return;
  }

  const result = await Swal.fire({
    icon: "question",
    title: `Xác nhận gửi ${selected.length} hóa đơn?`,
    showCancelButton: true,
    confirmButtonText: "Gửi",
    cancelButtonText: "Hủy",
    timer: 8000
  });

  if (!result.isConfirmed) {
    console.log("[DEBUG] ❌ Người dùng đã hủy xác nhận gửi");
    return;
  }

  try {
    console.log("[DEBUG] 🧊 Lock giao diện...");
    lockAlertShown = true;

    // ✅ Đặt cờ trước khi lock để kiểm soát trạng thái toàn hệ thống
	window.isInvoiceProcessing = true;
	window.isBatchSending = true;
	window.isOwnerOfBatchSending = true;
	window.isOwnerOfSingleSend = false;

    lockScreen();

    console.log("[DEBUG] 🚀 Gửi API /api/send_selected_logs");
    const res = await fetch("/api/send_selected_logs", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ codes })
    });

    const json = await res.json();
    console.log("[DEBUG] 📥 Phản hồi từ backend:", json);

    if (!json.success) {
      console.log("[DEBUG] ❌ Gửi thất bại, unlock giao diện");
      window.isInvoiceProcessing = false;
      window.isBatchSending = false;
      unlockScreen();

      await Swal.fire({
        icon: "error",
        title: "❌ Thất bại",
        text: json.message || "Không thể gửi hóa đơn.",
        timer: 8000,
        showConfirmButton: true
      });
    } else {
      console.log("[DEBUG] ✅ Gửi thành công. KHÔNG unlock – giữ Swal mở đến khi người dùng bấm Đóng");
      await fetchLogs();  // tuỳ chọn: cập nhật danh sách
      // ❌ Không unlock tại đây để backend xử lý xong mới đóng
    }

  } catch (err) {
    console.error("❌ [DEBUG] Lỗi khi gửi hàng loạt:", err);
    window.isInvoiceProcessing = false;
    window.isBatchSending = false;
    unlockScreen();

    await Swal.fire("Lỗi", "Không thể gửi hóa đơn. Vui lòng thử lại.", "error");
  }
}


//=========== THÔNG BÁO TRÊN MÀN HÌNH GỞI SAU X PHÚT, CHỪA Y PHÚT VÀ TỰ CHECKBOX XUẤT HÀNG LOẠT ==========
async function updateAutoConfigStatus() {
  const res = await fetch("/api/setting");
  const setting = await res.json();

  const enabled = setting.autoEveryXEnabled === true;
  const everyXMin = setting.autoEveryXMin || 30;
  const skipYMin = setting.autoSkipYMin || 30;
  const maxAmount = setting.autoMaxAmount || 0;
  const provider = (window.currentConfig?.provider || localStorage.getItem("currentProvider") || "vnpt").toUpperCase();

  const configStatus = document.getElementById("autoConfigStatus");
  if (!configStatus) return;

  configStatus.style.display = "inline-block";
  configStatus.style.color = "gray";

  let html = "";

  // === Chế độ 1
  const scheduleTimes = setting.autoScheduleTimes || [];
  if (setting.autoScheduleEnabled && scheduleTimes.length > 0) {
    const formattedTimes = scheduleTimes.map(t => {
      const [h, m] = t.split(":");
      return `${h}:${m}`;
    }).join(", ");
    html += `Chế độ 1: Xuất tất cả GD lúc <b>${formattedTimes}</b>`;
  }

  // === Chế độ 2
  if (enabled) {
    const now = new Date();
    const currMinutes = now.getHours() * 60 + now.getMinutes();
    const nextBlockMinutes = Math.ceil(currMinutes / everyXMin) * everyXMin;

    const nextRunTime = new Date(now);
    nextRunTime.setHours(Math.floor(nextBlockMinutes / 60), nextBlockMinutes % 60, 0, 0);

    const cutoffTime = new Date(nextRunTime.getTime() - skipYMin * 60 * 1000);

    const hhmmNext = nextRunTime.toLocaleTimeString("vi-VN", { hour: '2-digit', minute: '2-digit', hour12: false });
    const hhmmCutoff = cutoffTime.toLocaleTimeString("vi-VN", { hour: '2-digit', minute: '2-digit', hour12: false });
    const amountNote = maxAmount > 0 ? ` với số tiền <b>< ${maxAmount.toLocaleString("vi-VN")}đ</b>` : "";

    html += (html ? "<br>" : "") + `Chế độ 2: Xuất lúc <b>${hhmmNext}</b> và các GD trước <b>${hhmmCutoff}</b>${amountNote}`;
  }

  configStatus.innerHTML = html || "Chưa bật chế độ tự động.";
}



// === BẤM NÚT XUẤT GD RA EXCEL CÓ THEO BỘ LỌC ===
async function exportExcelByDate() {
  if (!currentFilteredLogs || currentFilteredLogs.length === 0) {
    alert("Không có giao dịch nào để xuất.");
    return;
  }

  const codes = currentFilteredLogs.map(log => log.transactionCode);

  const res = await fetch('/export_logs_excel', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ codes })  // Gửi danh sách mã GD đã lọc
  });

  if (!res.ok) {
    const result = await res.json().catch(() => ({}));
    alert(result.message || "Xuất file thất bại.");
    return;
  }

  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Giao_dich_${new Date().toISOString().slice(0, 10)}.xlsx`;
  a.click();
}


function sortTable(thElement) {
	const table = thElement.closest("table");
	const tbody = table.querySelector("tbody");
	const rows = Array.from(tbody.querySelectorAll("tr"));

	// Tính colIndex động theo vị trí thực tế của th
	const headers = Array.from(thElement.parentElement.children);
	const colIndex = headers.indexOf(thElement);

	const asc = table.dataset.sortCol == colIndex && table.dataset.sortDir !== "asc";
	table.dataset.sortCol = colIndex;
	table.dataset.sortDir = asc ? "asc" : "desc";

	const parseMoney = (str) => parseFloat(str.replace(/\./g, '').replace(/,/g, '.')) || 0;
	const parseLit = (str) => parseFloat(str.replace(",", ".")) || 0;
	const parseTime = (str) => {
		const parts = str.split(":").map(Number);
		return parts.length === 3 ? parts[0] * 3600 + parts[1] * 60 + parts[2] : 0;
	};

	rows.sort((a, b) => {
		let v1 = a.cells[colIndex]?.textContent.trim() || '';
		let v2 = b.cells[colIndex]?.textContent.trim() || '';

		let n1 = v1, n2 = v2;

		if (v1.includes("₫") || v1.match(/^\d{1,3}(\.\d{3})*(,\d+)?$/)) {
			n1 = parseMoney(v1);
			n2 = parseMoney(v2);
		} else if (v1.match(/^\d+,\d+$/) || v1.match(/^\d{1,3}(,\d{3})*$/)) {
			n1 = parseLit(v1);
			n2 = parseLit(v2);
		} else if (v1.match(/^\d{1,2}:\d{2}:\d{2}$/)) {
			n1 = parseTime(v1);
			n2 = parseTime(v2);
		} else {
			n1 = v1.toLowerCase();
			n2 = v2.toLowerCase();
		}

		if (typeof n1 === "string") {
			return asc ? n1.localeCompare(n2) : n2.localeCompare(n1);
		} else {
			return asc ? n1 - n2 : n2 - n1;
		}
	});

	tbody.innerHTML = "";
	rows.forEach(row => tbody.appendChild(row));
}



function renderPreviewForm(data) {
  document.getElementById("formHoTen").value = data.ho_ten || "";
  document.getElementById("formTen").value = data.ten || "";
  document.getElementById("formMST").value = data.mst || "";
  document.getElementById("formDChi").value = data.dchi || "";
  document.getElementById("formHTTToan").value = data.httt || "";
  document.getElementById("formTThue").value = data.thueStr || String(data.thue) || "";
  document.getElementById("formMKHang").value = data.mkhang || "";
  document.getElementById("formTHHDVu").value = data.thhdv || "";
}


// Định nghĩa hàm cập nhật người mua mặc định (nếu chưa có)
async function loadDefaultCustomerData() {
  try {
    const res = await fetch("/api/setting");
    const data = await res.json();

    document.getElementById("formHoTen").value = data.defaultCustomerFullName || "";
    document.getElementById("formTen").value = data.defaultCustomerName || "";
    document.getElementById("formMST").value = data.defaultCustomerTaxCode || "";
    document.getElementById("formDChi").value = data.defaultCustomerAddress || "";
    document.getElementById("formHTTToan").value = data.defaultCustomerHTTToan || "";
    document.getElementById("formTThue").value = data.defaultCustomerTThue || "";
    window.defaultCustomerTThue = data.defaultCustomerTThue || "";
    document.getElementById("formMKHang").value = data.defaultCustomerMKHang || "";
    document.getElementById("formEmail").value = data.defaultCustomerEmail || "";
    document.getElementById("formPlate").value = data.defaultCustomerPlate || "";
	document.getElementById("formCCCD").value = data.defaultCustomerCCCD || "";
    document.getElementById("formMdvqhns").value = data.defaultCustomerMdvqhns || "";	
    document.getElementById("formPhone").value = data.defaultCustomerPhone || "";
    document.getElementById("searchCustomerPopup").value = "";
  } catch (e) {
    console.error("❌ Không load được setting:", e);
  }
}



// Nút trạng thái đang cập nhật log
function updateFetchStatus(status) {
  const el = document.getElementById("fetchStatus");
  if (!el) return;
  el.innerText = status;
}

// ===== Phân trang hiển thị log ====================
function renderPagination() {
  const totalLogs = currentFilteredLogs.length;
  const totalPages = Math.ceil(totalLogs / logsPerPage);

  // Hiển thị thông tin
  const start = (currentPage - 1) * logsPerPage + 1;
  const end = Math.min(currentPage * logsPerPage, totalLogs);
  document.getElementById("paginationInfo").textContent = `Đang xem ${start} - ${end}/${totalLogs} giao dịch`;

  // Tạo số trang
  const numbers = [];
  for (let i = 1; i <= totalPages; i++) {
    numbers.push(`<button onclick="goToPage(${i})" class="${i === currentPage ? 'active' : ''}">${i}</button>`);
  }

  document.getElementById("paginationNumbers").innerHTML = numbers.slice(0, 10).join('');
}

function renderTableByPage() {
  const start = (currentPage - 1) * logsPerPage;
  const end = currentPage * logsPerPage;
  renderTable(currentFilteredLogs.slice(start, end));
}

function goToPage(page) {
  currentPage = page;
  renderPagination();
  renderTableByPage();
}

function goToFirstPage() {
  currentPage = 1;
  renderPagination();
  renderTableByPage();
}

function goToLastPage() {
  const totalPages = Math.ceil(currentFilteredLogs.length / logsPerPage);
  currentPage = totalPages;
  renderPagination();
  renderTableByPage();
}

function goToPrevPage() {
  if (currentPage > 1) currentPage--;
  renderPagination();
  renderTableByPage();
}

function goToNextPage() {
  const totalPages = Math.ceil(currentFilteredLogs.length / logsPerPage);
  if (currentPage < totalPages) currentPage++;
  renderPagination();
  renderTableByPage();
}

function changeLogsPerPage() {
  logsPerPage = Number(document.getElementById("logsPerPageSelect").value);
  currentPage = 1;
  renderPagination();
  renderTableByPage();
  fetchLogs();  // hoặc render lại trang
}

function showModal(id) {
  const el = document.getElementById(id);
  if (el) el.classList.add("show");
}
window.showModal = showModal;
window.closePreview = closePreview;

// ===== Bấm hình con mắt xem popup hóa đơn ====================
function previewLogByCode(code) {
  const checkboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
  const selectedCodes = checkboxes.map(cb => cb.value);

  // Nếu chọn nhiều log → xử lý gộp
  if (selectedCodes.length > 1 && selectedCodes.includes(code)) {
    return previewMergedLogs(selectedCodes);
  }

  // Ngược lại giữ nguyên logic cũ
  const log = allLogs.find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy log.");

  const config = (window.configs || []).find(c => c.stationCode === log.stationCode);
  if (config) window.currentConfig = config;

  // ✅ FIX: Use the provider from the specific log/invoice, not the global setting
  // This ensures that Viettel invoices use Viettel preview function, BKAV invoices use BKAV, etc.
  let provider;
  if (log.provider && log.provider.trim() !== "") {
    // Use the provider that was used to create this specific invoice
    provider = log.provider.toLowerCase();
    console.log(`🔍 [PREVIEW] Using log-specific provider: ${provider} for transaction: ${code}`);
  } else {
    // Fallback to global provider setting for logs without provider info
    provider = (window.currentConfig?.provider || localStorage.getItem("currentProvider") || "vnpt").toLowerCase();
    console.log(`🔍 [PREVIEW] Using global provider: ${provider} for transaction: ${code}`);
  }

  const handler = InvoiceProviderHandlers[provider];
  if (handler?.preview) return handler.preview(code);

  alert(`❌ Không hỗ trợ xem hóa đơn cho nhà cung cấp: ${provider}`);
}

// ========== Update preview khi gộp log (checkbox 2 log trở lên cùng nhiên liệu) ==========
// Gộp các log được chọn thành 1 preview log tổng
function previewMergedLogs(codes) {
  if (!Array.isArray(codes) || codes.length < 2) {
    return alert("⚠️ Phải chọn từ 2 log trở lên để gộp.");
  }

  const selectedLogs = allLogs.filter(log => codes.includes(log.transactionCode));
  if (selectedLogs.length !== codes.length) {
    return alert("❌ Một số log không hợp lệ.");
  }

  // Kiểm tra cùng nhiên liệu và đơn giá
  const fuelType = selectedLogs[0].fuelType;
  const price = selectedLogs[0].transactionPrice;
  const isSame = selectedLogs.every(log => log.fuelType === fuelType && log.transactionPrice === price);

  if (!isSame) {
    return alert("⚠️ Các log phải cùng nhiên liệu và đơn giá.");
  }

	const now = new Date();

	const merged = {
	  ...selectedLogs[0],
	  transactionCode: `999999_${(d=>`${d.getDate().toString().padStart(2,'0')}${(d.getMonth()+1).toString().padStart(2,'0')}${d.getFullYear().toString().slice(-2)}`)(now)}_${now.toTimeString().slice(0,8).replaceAll(':','')}`,
	  transactionCost: selectedLogs.reduce((sum, l) => sum + l.transactionCost, 0),
	  transactionAmount: selectedLogs.reduce((sum, l) => sum + l.transactionAmount, 0),
	  // ✅ FIX: Add missing transactionPrice for VIETTEL merged invoice
	  transactionPrice: selectedLogs.reduce((sum, l) => sum + l.transactionPrice, 0),
	  invoiceNumber: null,
	  invoiceId: null,
	  nl: selectedLogs[0].transactionDate,
	  gopFromCodes: codes  // dùng sau để gửi hóa đơn và hiển thị
	};

  window.previewData = merged;
  updatePreview();  // hiện popup như 1 log bình thường
  showModal("xmlPreview");
}

// =============== Bấm gửi hóa đơn từ Popup đã gộp log ================
async function sendMergedInvoice() {
  const d = window.previewData;
  if (!d || !d.transactionCode?.startsWith("999999_")) {
    return alert("❌ Không phải log gộp.");
  }

  lockScreen();  // Khóa giao diện trước khi gửi

  const customData = {
    ho_ten: document.getElementById("formHoTen")?.value || "",
    ten: document.getElementById("formTen")?.value || "",
    mst: document.getElementById("formMST")?.value || "",
    dchi: document.getElementById("formDChi")?.value || "",
    httt: document.getElementById("formHTTToan")?.value || "",
    thue: Number(document.getElementById("formTThue")?.value || ""),
    thueStr: document.getElementById("formTThue")?.value || "",
    mkhang: document.getElementById("formMKHang")?.value || "",
    email: document.getElementById("formEmail")?.value || "",
    plate: document.getElementById("formPlate")?.value || "",
    mdvqhns: document.getElementById("formMdvqhns")?.value || "",
    phone: document.getElementById("formPhone")?.value || "",
    cccd: document.getElementById("formCCCD")?.value || "",
    thhdv: document.getElementById("formTHHDVu")?.value || ""
  };

  const payload = {
    previewData: d,
    gopFromCodes: d.gopFromCodes || [],
    customData
  };

  try {
    const res = await fetch('/create_invoice_from_preview', {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload)
    });

    const result = await res.json();
    console.log("📥 Phản hồi khi gửi log gộp:", result);

	if (result.success) {
	  const shdon = result?.invoiceNumber || result?.shdon || result?.verificationCode || "???";
	  await Swal.fire("Xuất hóa đơn gộp thành công", `Số hóa đơn: <b style="color:green">${shdon}</b>`, "success");
	  closePreview();
	  fetchLogs();
	} else {
	  const errorMsg = result?.message || result?.response || JSON.stringify(result) || "Không gửi được hóa đơn log gộp.";
	  await Swal.fire("❌ Lỗi đơn vị hóa đơn", errorMsg, "error");
	  closePreview();   
	  fetchLogs();  
	}

  } catch (e) {
    console.error("❌ Lỗi gửi hóa đơn log gộp:", e);
    await Swal.fire("❌ Lỗi hóa đơn", "Không gửi được hóa đơn gộp.", "error");
  } finally {
    unlockScreen();  // Mở khóa giao diện sau khi hoàn tất
  }

}

// =================== Giao dịch nội bộ ===========================================================
function markInternalLogs() {
  const selected = Array.from(document.querySelectorAll('.row-checkbox:checked'));
  if (selected.length !== 1) {
    return alert("Vui lòng chỉ chọn 1 giao dịch để đánh dấu nội bộ.");
  }

  const code = selected[0].value;
  currentInternalCode = code;
  document.getElementById("internalNoteContent").value = ""; // reset nội dung
  new bootstrap.Modal(document.getElementById("internalNoteModal")).show();
}

// ============== Popup lưu Giao dịch nội bộ =======================================================
async function saveInternalNote() {
  const note = document.getElementById("internalNoteContent").value.trim();
  if (!note) return alert("Vui lòng nhập lý do để giải trình với cơ quan thuế.");

  const res = await fetch("/api/mark_internal_logs", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ code: currentInternalCode, note })
  });

  const result = await res.json();
  if (result.success) {
    alert("✅ Đã tạo lý do giao dịch nội bộ.");

    // Ẩn modal đúng cách
    const modalEl = document.getElementById("internalNoteModal");
    const modal = bootstrap.Modal.getInstance(modalEl);
    modal?.hide();

    fetchLogs(); // refresh lại bảng
  } else {
    alert(result.message || "Lỗi khi lưu.");
  }
}

// ===== Bấm icon xem lại Giao dịch nội bộ ====================
async function showInternalNote(code) {
  currentInternalCode = code;
  const res = await fetch(`/api/internal_note?code=${code}`);
  const result = await res.json();
  if (result.success) {
    document.getElementById("internalNoteContent").value = result.note || "";
    new bootstrap.Modal(document.getElementById("internalNoteModal")).show();
  } else {
    alert("Không tìm thấy nội dung.");
  }
}
function closeInternalNotePopup() {
  const modalEl = document.getElementById("internalNoteModal");
  if (modalEl) bootstrap.Modal.getInstance(modalEl)?.hide();
}

// ===== Xóa Giao dịch nội bộ ====================
async function clearInternalNote() {
  if (!confirm("Bạn có chắc muốn bỏ đánh dấu nội bộ và xóa lý do?")) return;

  const res = await fetch("/api/clear_internal_note", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ code: currentInternalCode })
  });

  const result = await res.json();
  if (result.success) {
    alert("✅ Đã bỏ đánh dấu nội bộ.");
    closeInternalNotePopup();
    fetchLogs(); // cập nhật lại bảng
  } else {
    alert(result.message || "Xóa thất bại.");
  }
}

// ===== Khóa giao diện khi xuất hóa đơn ====================
function lockScreen() {
  // 🟢 Đặt cờ cục bộ để biết máy này đang xử lý
  window.isInvoiceProcessing = true;

  console.log("[DEBUG] 🔒 Hiển thị Swal lockScreen (bất kể máy nào)");

  Swal.fire({
    title: '<span style="font-size: 22px; color: #063970">⏳ Đang xuất hóa đơn...</span>',
    html: '<p style="font-size: 16px;">Vui lòng chờ hoặc bấm <b>Đóng</b> để dừng.</p>',
    allowOutsideClick: false,
    allowEscapeKey: false,
    showCancelButton: true,
    cancelButtonText: 'Đóng',
    cancelButtonColor: '#6c757d',
    showConfirmButton: false,
    backdrop: true,
    position: 'top',
    padding: '0.5rem',
    customClass: {
      title: 'swal2-title-large',
      popup: 'swal2-small-popup'
    },
    willOpen: () => {
      Swal.showLoading();
    }
  }).then((result) => {
    // ✅ Khi người dùng bấm nút “Đóng” hoặc ESC
    if (result.dismiss === Swal.DismissReason.cancel) {
      console.warn("[DEBUG] 🔁 Người dùng bấm Đóng – yêu cầu dừng xuất hóa đơn");
      unlockScreen(); // Gọi luôn
    }
  });
}

// ===== Mở khóa giao diện khi xuất hóa đơn, chỉ máy đang gửi mới được tự unlock ====================
function unlockScreen() {
  console.log("[DEBUG] 🔓 Thực thi unlockScreen()");
  Swal.close();

  // ✅ Chỉ gửi unlock backend nếu là người gửi hàng loạt HOẶC người gửi đơn
  if (window.isOwnerOfBatchSending === true || window.isOwnerOfSingleSend === true) {
    console.log("[DEBUG] ✅ Máy có quyền unlock backend → gửi yêu cầu dừng");
    fetch("/api/invoice-lock", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ isProcessing: false })
    }).then(() => {
      console.log("[DEBUG] 🟢 Gửi unlock backend thành công");
    }).catch((err) => {
      console.warn("[DEBUG] ⚠️ Lỗi khi unlock backend:", err);
    });
  } else {
    console.warn("[DEBUG] ⛔ Máy này không phải người gửi → KHÔNG unlock backend");
  }

  // ✅ Reset trạng thái giao diện local
  window.isInvoiceProcessing = false;
  window.isBatchSending = false;
  window.isOwnerOfBatchSending = false;
  window.isOwnerOfSingleSend = false;

  fetchLogs(); // làm mới bảng log
}


// ===== Thanh toán QR Code ====================
async function generateQR(code) {
  const log = (window.allLogs || window.currentLogs || []).find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy giao dịch.");

  let accountNo = "", accountName = "", acqId = "", branch = "";
  try {
    const res = await fetch("/api/bank");
    const bank = await res.json();
    accountNo = bank.qrAccountNo || "************";
    accountName = bank.qrAccountName || "Tên tài khoản";
    acqId = bank.qrBankCode || "970403";
    branch = bank.qrBankBranch || "";
  } catch (e) {
    return alert("❌ Không thể lấy thông tin ngân hàng.");
  }


  const fuelName = getMappedFuelName(log.fuelType) || "Không rõ";
  const cost = log.transactionCost;
  const volume = log.transactionAmount;
  const price = log.transactionPrice;
  const addInfo = `THANH TOAN CHO GIAO DICH ${code}`;
  const encodedName = encodeURIComponent(accountName);
  const encodedInfo = encodeURIComponent(addInfo);


  const qrImageUrl = `https://img.vietqr.io/image/${acqId}-${accountNo}-compact.png?accountName=${encodedName}&addInfo=${encodedInfo}&amount=${cost}`;

  Swal.fire({
	  html: `
		<div style="text-align:center">
		  <h5 style="margin-bottom: 4px;">THÔNG TIN CHUYỂN KHOẢN</h5>
		  <div style="font-weight: bold; font-size: 15px; color: #333;">
			${accountName}<br/>
			STK: ${accountNo}<br/>
			<!-- ${branch} -->
		  </div>

		  <img id="qrImage" src="${qrImageUrl}" style="max-width: 100%; margin: 8px 0; border-radius: 8px;" />
		  <p style="font-size: 13px; font-weight: bold; color: #dc3545;">${addInfo}</p>

		  <div style="font-size: 14px; text-align: left; margin: 12px auto; max-width: 320px;">
			<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
			  <span style="font-weight: bold; color: black;">Nhiên liệu:</span>
			  <span style="font-weight: bold; color: #063970;">${fuelName}</span>
			</div>
			<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
			  <span style="font-weight: bold; color: black;">Số tiền:</span>
			  <span style="font-weight: bold; color: #063970;">${cost.toLocaleString('vi-VN')} đồng</span>
			</div>
			<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
			  <span style="font-weight: bold; color: black;">Số lít:</span>
			  <span style="font-weight: bold; color: #063970;">${formatLit(volume)} lít</span>
			</div>
			<div style="display: flex; justify-content: space-between;">
			  <span style="font-weight: bold; color: black;">Đơn giá:</span>
			  <span style="font-weight: bold; color: #063970;">${price.toLocaleString('vi-VN')} đồng/lít</span>
			</div>
		  </div>

		  <div style="margin-top: 12px; text-align: center;">
			<button id="btnMarkPaid" style="background-color: #198754; color: white; border: none; padding: 6px 14px; border-radius: 6px; margin-right: 10px;">
			  Đã chuyển khoản
			</button>
			<button id="btnCloseQR" style="background-color: #6c757d; color: white; border: none; padding: 6px 14px; border-radius: 6px;">
			  Đóng
			</button>
		  </div>
		</div>
	  `,
    showConfirmButton: false,
    showCloseButton: true,
    width: 380,

    didOpen: () => {
		document.getElementById("btnMarkPaid").onclick = async () => {
		  const qrIcon = document.getElementById(`qr-icon-${code}`);
		  if (qrIcon) {
			qrIcon.classList.remove("text-primary");
			qrIcon.classList.add("text-success");
		  }

		  const res = await fetch("/api/mark_qr_paid", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({ transactionCode: code })
		  });

		  const result = await res.json();
		  if (result.success) {
			  Swal.fire({
				icon: "success",
				title: "Đã ghi nhận thanh toán",
				text: "Biểu tượng QR đã đổi màu xanh lá.",
				timer: 3000,
				showConfirmButton: false
			  });

			  if (typeof loadResetLogs === "function") {
				await loadResetLogs();
			  } else if (typeof fetchLogs === "function") {
				await fetchLogs();
			  } else {
				location.reload();
			  }
		  } else {
			Swal.fire({
			  icon: "error",
			  title: "❌ Lỗi",
			  text: result.message || "Không thể ghi nhận thanh toán.",
			});
		  }
		};

        document.getElementById("btnCloseQR").onclick = () => {
          Swal.close();
        };
    }
	
  });
}
window.generateQR = generateQR;

// ===== Kiểm tra thiết bị máy khác có đang xuất hóa đơn ? ====================
async function checkInvoiceLock() {
  try {
    const res = await fetch("/api/invoice-lock", { method: "GET" });
    const json = await res.json();
    return !json.isProcessing; // trả về true nếu được phép gửi
  } catch (e) {
    console.error("❌ Lỗi kiểm tra lock:", e);
    return false;
  }
}


async function lockInvoiceSend() {
  await fetch("/api/invoice-lock", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ isProcessing: true }),
  });
}

async function unlockInvoiceSend() {
  await fetch("/api/invoice-lock", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ isProcessing: false }),
  });
}


let lockAlertShown = false;

// Đếm GD đang xuất X/Y và xử lý máy A xuất, máy B locksreen
setInterval(async () => {
  try {
    const res = await fetch("/api/invoice-lock");
    const json = await res.json();

    // ❌ Không hiển thị lockScreen nếu máy khác đang gửi
    // ❌ Không dùng biến lockAlertShown nữa
    // ✅ Nếu chính máy này đang gửi, và backend đã hết → unlock giao diện
	if (!json.isProcessing && window.isOwnerOfBatchSending === true) {
	  console.log("✅ Backend đã hết gửi – máy gửi unlock giao diện");
	  unlockScreen();
	}

    // 🆕 Lấy số hóa đơn đã gửi trong chế độ 3
    try {
      const statRes = await fetch("/api/invoice-stats");
      const statData = await statRes.json();
      const sentEl = document.getElementById("sentCount");
      const totalEl = document.getElementById("totalCount");

      if (sentEl) sentEl.innerText = statData.sent || 0;
      if (totalEl) totalEl.innerText = statData.total || 0;

    } catch (e) {
      console.warn("Không thể lấy dữ liệu /api/invoice-stats:", e);
    }

  } catch (e) {
    console.error("❌ Lỗi kiểm tra trạng thái gửi hóa đơn:", e);
  }
}, 4000);



// === Tự F5 index khi qua ngày mới ======================
function scheduleReloadAtMidnight() {
  const now = new Date();
  const midnight = new Date();

  midnight.setHours(24, 0, 0, 0); // 00:00:00 ngày mai

  const msUntilMidnight = midnight.getTime() - now.getTime();

  console.log(`⏰ Tự động F5 sau ${(msUntilMidnight / 1000 / 60).toFixed(2)} phút`);

  setTimeout(() => {
    console.log("🔁 Đến ngày mới, đang F5 lại trang...");
    location.reload();
  }, msUntilMidnight);
}

scheduleReloadAtMidnight();

// === Xử lý đếm số lượng người truy cập ======================
async function showActiveUsers() {
  try {
    const res = await fetch('/active_users');
    const data = await res.json();
    document.getElementById('activeUserCount').innerText = `🟢 Online: ${data.active}`;
  } catch (e) {
    console.warn("Không thể lấy số người dùng hoạt động.");
  }
}

setInterval(showActiveUsers, 15000); // cập nhật mỗi 15 giây

// === Xử lý máy khác truy cập mở khóa backend ======================
setInterval(async () => {
  try {
    const res = await fetch("/active_users");
    const data = await res.json();

    if (data.active > 1 && window.isBatchSending) {
      console.log("⚠️ Backend báo active > 1, nhưng KHÔNG tự unlock nữa.");
    }

    // Nếu chỉ còn 1 máy đang online, và vẫn đang bị khóa → tự unlock
	if (data.active <= 1 && window.isBatchSending && !window.isInvoiceProcessing) {
	  console.warn("✅ Chỉ còn 1 active, tự unlock tránh kẹt.");
	  unlockScreen();
	}

  } catch (e) {
    console.warn("❌ Lỗi khi kiểm tra active_users:", e);
  }
}, 10000);


// === Xử lý nếu thu nhỏ trình duyệt hơn 1 phút thì mở khóa backend ======================
let hiddenSince = null;

document.addEventListener("visibilitychange", () => {
  if (document.hidden) hiddenSince = Date.now();
  else hiddenSince = null;
});

setInterval(async () => {
	if (hiddenSince && Date.now() - hiddenSince > 60000 && window.isBatchSending) {
	  console.warn("⚠️ Tab bị ẩn > 60s → tự unlock backend");
	  await fetch("/api/invoice-lock", {
		method: "POST",
		headers: { "Content-Type": "application/json" },
		body: JSON.stringify({ isProcessing: false })
	  });
	  window.isBatchSending = false;

	  await Swal.fire({
		icon: "info",
		title: "⚠️ Phiên làm việc đã bị dừng",
		text: "Trình duyệt bị ẩn quá lâu nên hệ thống đã hủy gửi hóa đơn.",
		timer: 5000
	  });

	  location.reload();
	}
}, 10000);

// === Xử lý backup sql từ setting và auto_backup.py ======================
async function saveBackupConfig() {
  const backupDir = document.getElementById("backupDir")?.value || "backup";
  const backupInterval = Number(document.getElementById("backupInterval")?.value || 60);

  const res = await fetch("/api/backup-config", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ backupDir, backupInterval })
  });

  const result = await res.json();
  if (result.success) {
    alert("✅ Đã lưu cấu hình auto backup.");
  } else {
    alert("❌ Không thể lưu cấu hình backup.");
  }
}


// Gán global các hàm popup (nếu chưa có)
window.previewLogByCode = previewLogByCode;
window.loadDefaultCustomerData = loadDefaultCustomerData;
window.closePreview = closePreview;
window.showModal = showModal;
window.saveBackupConfig = saveBackupConfig;

// Sau khi các provider đã khai báo xong các hàm, gán lại map toàn cục
window.InvoiceProviderHandlers = {
  vnpt: {
    preview: window.previewXML_VNPT,
	send: window.sendInvoiceFromPopup_VNPT
  },
  mobifone: {
    preview: window.previewXML_MOBIFONE,    
	send: window.sendInvoiceFromPopup_MOBIFONE
  },
  viettel: {
    preview: window.previewXML_VIETTEL,
	send: window.sendInvoiceFromPopup_VIETTEL
  },
  misa: {
    preview: window.previewXML_MISA,
	send: window.sendInvoiceFromPopup_MISA
  },
  easy: {
    preview: window.previewXML_EASY,
	send: window.sendInvoiceFromPopup_EASY
  },
  bkav: {
    preview: window.previewXML_BKAV,
	send: window.sendInvoiceFromPopup_BKAV
  },
  fast: {
    preview: window.previewXML_FAST,
	send: window.sendInvoiceFromPopup_FAST
  },
  hilo: {
    preview: window.previewXML_HILO,    
	send: window.sendInvoiceFromPopup_HILO
  },  
  mobifone_test: {
    preview: window.previewXML_MOBIFONE_TEST,    
	send: window.sendInvoiceFromPopup_MOBIFONE_TEST
  }
};

// Bắt sự kiện lưu từ setting.html
window.addEventListener("storage", function(e) {
  if (e.key === "settingsUpdatedAt") {
    console.log("🔄 Phát hiện cấu hình mới, nạp lại người mua mặc định");
    loadDefaultCustomerData();

    Swal.fire({
      icon: "info",
      title: "⚙️ Cài đặt đã được cập nhật",
      text: "Thông tin người mua đã được nạp lại.",
      timer: 2500,
      showConfirmButton: false
    });
  }
});
