// invoice_providers/vnpt.js
async function previewXML_VNPT(code) {
  console.log("📥 [VNPT] Gọi xem trước hóa đơn cho:", code);
	// Reset dữ liệu trước khi gọi lại
  if (window.previewData) {
	  window.previewData.discountPerLit = 0;
	  window.previewData.discountTotal = 0;
	  window.previewData.chu = "";
  }
  document.getElementById("formDiscountPerLit").value = "0";
  
 // Tìm log theo mã giao dịch
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy giao dịch.");
	
 // Kiểm tra, nếu có số hóa đơn thì không cho xem popup nữa (Dành cho các bên hóa đơn không có API tải PDF)
	const inv = log.invoiceNumber?.trim() || "";
	const isDaCoHoaDon = inv &&
	  inv !== "CHUA_RO_SOHĐ" &&
	  inv !== log.transactionCode &&
	  /^[0-9]{4,20}$/.test(inv);


	if (log.isInvoiced || isDaCoHoaDon) {
	  await Swal.fire("⛔ Đã có hóa đơn", `Giao dịch ${code} không thể gửi lại.`, "warning");
	  return;
	}

  const invoiceId = log.invoiceId;
  const hasInvoice = isValidInvoiceNumber(log.invoiceNumber);
  
  console.log("🟨 Kiểm tra invoiceNumber:", log.invoiceNumber);

  let thueStr = document.getElementById("formTThue")?.value || "10";
  let thue = 10;

  if (thueStr === "KCT" || thueStr === "KKKNT") {
    thue = 0;
  } else if (thueStr !== null && thueStr !== "") {
    thue = parseFloat(thueStr);
  }

  const res = await fetch('/preview_invoice', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      transactionCode: code,
      customData: {
        ho_ten: document.getElementById("formHoTen")?.value || "",
        ten: document.getElementById("formTen")?.value || "",
        mst: document.getElementById("formMST")?.value || "",
        dchi: document.getElementById("formDChi")?.value || "",
        httt: document.getElementById("formHTTToan")?.value || "",
        thue: thue,
        thueStr: thueStr,
        mkhang: document.getElementById("formMKHang")?.value || "",
        thhdv: getMappedFuelName(window.previewData?.fuelType || ""),
        discountPerLitVat: document.getElementById("formDiscountPerLit")?.value || "0",
        email: document.getElementById("formEmail")?.value || "",
        plate: document.getElementById("formPlate")?.value || "",
		mdvqhns: document.getElementById("formMdvqhns")?.value || "",	
		phone: document.getElementById("formPhone")?.value || "",
        cccd: document.getElementById("formCCCD")?.value || ""
      }
    })
  });

  const result = await res.json();
  if (!result.success || !result.xml) {
    alert("Không thể xem trước hóa đơn (không có XML)");
    return;
  }

  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(result.xml, "text/xml");
  const nl = xmlDoc.querySelector("NLap")?.textContent || "";

  // ✅ Gán dữ liệu đầy đủ từ backend vào previewData
  window.previewData = {
    ...log,
    nl,
    sl: Number(result.sl),
    dgChuaVat: Number(result.dgChuaVat),
    ttVat: Number(result.ttVat),
    ttChuaVat: Number(result.ttChuaVat),
    tienThue: Number(result.tienThue),
    discountPerLit: Number(result.discountPerLit),
    discountTotal: Number(result.discountTotal),
    congtienhang: Number(result.congtienhang),
    chu: result.chu || ""
  };

  loadDefaultCustomerData();

  document.getElementById("xmlPreview")?.classList.remove("hidden");
  showModal("xmlPreview");
  updatePreview();

}

// ======== Gửi từ popup VNPT =========================================
async function sendInvoiceFromPopup_VNPT() {
  const code = window.previewData?.transactionCode;
  if (!window.previewData || !window.previewData.transactionCode) {
    await Swal.fire({
        icon: "error",
        title: " ❌ Không tìm thấy mã giao dịch.",
		timer: 8000,
        showConfirmButton: true
    });
    return;
  }

  // Nếu là log gộp (999999_xxx) thì gọi hàm riêng
  if (code.startsWith("999999_")) {
    return sendMergedInvoice(); // Đã định nghĩa trong main.js
  }

  // Tiếp tục logic như cũ cho log đơn
  const matched = currentFilteredLogs.find(l => l.transactionCode === code);

  if (matched) {
    const inv = matched.invoiceNumber?.trim() || "";
    const isDaCoHoaDon = inv &&
      inv !== "CHUA_RO_SOHĐ" &&
      inv !== matched.transactionCode &&
      /^[0-9]{4,20}$/.test(inv);

    if (matched.isInvoiced || isDaCoHoaDon) {
      await Swal.fire("⛔ Đã có hóa đơn", `Giao dịch ${code} đã có hóa đơn. Không thể gửi lại.`, "warning");
      return;
    }
  }

  const customData = {
    ho_ten: document.getElementById("formHoTen")?.value || "",
    ten: document.getElementById("formTen")?.value || "",
    mst: document.getElementById("formMST")?.value || "",
    dchi: document.getElementById("formDChi")?.value || "",
    httt: document.getElementById("formHTTToan")?.value || "",
	thue: document.getElementById("formTThue")?.value || "",
    mkhang: document.getElementById("formMKHang")?.value || "",
    thhdv: document.getElementById("formTHHDVu")?.value || "",
    email: document.getElementById("formEmail")?.value || "", 
    plate: document.getElementById("formPlate")?.value || "",
    mdvqhns: document.getElementById("formMdvqhns")?.value || "",	
	phone: document.getElementById("formPhone")?.value || "", 
    cccd: document.getElementById("formCCCD")?.value || "",
	discountPerLitVat: document.getElementById("formDiscountPerLit")?.value || "0"
  };

  window.isInvoiceProcessing = true;
  window.isBatchSending = false;
  window.isOwnerOfSingleSend = true;
  window.isOwnerOfBatchSending = false;
    
  await lockInvoiceSend();
  lockScreen();

  try {
    const res = await fetch('/create_invoice', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionCode: code, customData })
    });

    const result = await res.json();
    let msg = "";
    let icon = "success";

    if (result.success) {
      const match = result.response?.match(/\b\d{10,11}\b/);
      msg = match ? `✅ Số hóa đơn là: <b>${match[0]}</b>` : "✅ Thành công (không rõ số hóa đơn)";
    } else {
      let raw = result.response || result.message || "Không có phản hồi từ VNPT.";
      msg = `❌ VNPT trả lỗi:<br><pre style="text-align:left; white-space:pre-wrap;">${raw}</pre>`;
      icon = "error";
    }

    await Swal.fire({
      icon: icon,
      title: `Đã gửi: ${code}`,
      html: msg,
      timer: 8000,
      showConfirmButton: true
    });

    closePreview();
    loadDefaultCustomerData();
    fetchLogs();

  } catch (err) {
    console.error("❌ Lỗi khi gửi hóa đơn VNPT:", err);
    await Swal.fire({
      icon: "error",
      title: "❌ Gửi hóa đơn thất bại",
      text: "Vui lòng thử lại.",
      html: msg,
      timer: 20000,
      showConfirmButton: true
    });
  } finally {
    await unlockInvoiceSend();
    window.isInvoiceProcessing = false;
    unlockScreen();
	//location.reload();
  }
}

// Gán global để gọi từ main.js
window.sendInvoiceFromPopup_VNPT = sendInvoiceFromPopup_VNPT;
window.previewXML_VNPT = previewXML_VNPT;
