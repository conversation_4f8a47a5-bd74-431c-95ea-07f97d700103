<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>MontechPOS</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    .summary-bar {
      margin-top: 10px;
      margin-bottom: 15px;
      font-weight: bold;
      font-size: 14px;
      background-color: #f8f9fa;
      padding: 8px 12px;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 10px;
    }
  .modal {
	position: fixed;
	top: 0; left: 0;
	width: 100vw; height: 100vh;
	background-color: rgba(0,0,0,0.5);
	align-items: center;
	justify-content: center;
	display: none; /* mặc định ẩn */
  }

  .modal.show {
	display: flex !important; /* khi có class .show thì hiển thị */
  }

  .close {
	float: right;
	font-size: 24px;
	cursor: pointer;
  }
  .modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  }

  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body class="d-flex flex-column min-vh-100">
  <div class="container-fluid px-3 flex-grow-1">
	<div class="header-row d-flex align-items-center justify-content-between flex-wrap">
	  <!-- Logo và tên công ty -->
	  <div class="d-flex align-items-center gap-2">
		<img src="/static/favicon.ico" alt="Logo" style="height: 40px; border-radius: 8px; cursor: pointer;" onclick="location.reload()" />
		<div>
		  <h2 id="sellerName" class="m-0" style="color:  #dc3545; font-weight: bold; font-size: 20px;">CÔNG TY MONTECH</h2>
			<p class="m-0" style="font-size: 12px; font-weight: bold; color: #063970; white-space: nowrap;">
			  <span id="sellerTaxcode">**********</span>
			  <span id="sellerFullName">Montech</span>
			  <span id="providerLabel" class="text-muted ms-1" style="font-weight: normal;"></span>
			</p>
		</div>
	  </div>

	  <!-- Phần bên phải: Cập nhật + Cài đặt + User -->
	  <div class="d-flex align-items-center gap-3">
		<!-- ✅ Trạng thái cập nhật -->
		<div id="fetchStatus" style="font-size: 14px; color: gray; text-align: center;"></div>
		<div id="activeUserCount" style="font-size: 14px; color: gray; text-align: center;"></div>

		<!-- ✅ Nút cài đặt có dropdown mở tab mới -->
		{% set username = session.get('username') %}
		{% set role = session.get('role') %}

		{% if role == 'admin' %}
		  <div class="dropdown">
			<button class="btn btn-sm dropdown-toggle btn-setting" data-bs-toggle="dropdown" title="Cài đặt hệ thống">
			  <i class="fa-solid fa-gear"></i> Cài đặt
			</button>
			<ul class="dropdown-menu dropdown-menu-end">
			  <li><a class="dropdown-item" href="/check_invoice" target="_blank">📊 Quản lý xuất hóa đơn</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/customer" target="_blank">🧑‍💼 Quản lý khách hàng</a></li>
			  <li><hr class="dropdown-divider"></li>			  
			  <li><a class="dropdown-item" href="/setting" target="_blank">🛠️ Cài đặt xuất tự động</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/config_invoice" target="_blank">🧬 Cấu hình hóa đơn</a></li>
			  <li><hr class="dropdown-divider"></li>
			  <li><a class="dropdown-item" href="/bank_setting" target="_blank">💳 Thanh toán QR</a></li>

			  {% if username == 'montech' %}
				<li><hr class="dropdown-divider"></li>
				<li><a class="dropdown-item" href="/check_log" target="_blank">🔐 Check giao dịch</a></li>
				<li><hr class="dropdown-divider"></li>
				<li><a class="dropdown-item" href="/debug" target="_blank">🧰 ️Check debug</a></li>
			  {% endif %}
			</ul>
		  </div>
		{% endif %}

		<!-- Icon user và tên -->
		<div class="dropdown">
		  <button class="btn btn-sm dropdown-toggle btn-setting d-flex align-items-center gap-2" data-bs-toggle="dropdown" title="Tài khoản">
			<i class="fa-solid fa-user"></i>
			<span>{{ session.get('username') }}</span>
		  </button>
		  <ul class="dropdown-menu dropdown-menu-end">
			{% if role == 'admin' %}
			  <li><a class="dropdown-item" href="/user_manage" target="_blank">🕵️‍♂️ Quản lý tài khoản</a></li>
			{% endif %}
			<li><hr class="dropdown-divider"></li>
			<li><a class="dropdown-item text-danger" href="/logout">👋 Đăng xuất</a></li>
		  </ul>
		</div>
		
	  </div>
	  
	</div>
    <div style="height: 1px; background-color: #ccc; margin: 6px 0;"></div>

    <!-- Bộ lọc -->
	 <div class="row g-1 align-items-end mb-1 text-center">
	  <div class="col">
		 <label for="filterDate" class="form-label w-100">Ngày giao dịch</label>
		 <input type="date" id="filterDate" class="form-control" />
	  </div>

	  <div class="col">
		<label for="filterStartTime" class="form-label w-100">Từ Giờ</label>
		<input type="time" id="filterStartTime" class="form-control" />
	  </div>
	  <div class="col">
		<label for="filterEndTime" class="form-label w-100">Đến Giờ</label>
		<input type="time" id="filterEndTime" class="form-control" />
	  </div>
	  <div class="col">
		<label for="filterCode" class="form-label w-100">Tìm Nhanh</label>
		<input type="text" id="filterCode" class="form-control" placeholder="Tiền hoặc mã GD..." />
	  </div>
	  <div class="col">
		<label for="filterFaucet" class="form-label w-100">Cò Bơm</label>
		<select id="filterFaucet" class="form-select"></select>
	  </div>
	  <div class="col">
		<label for="filterFuel" class="form-label w-100">Nhiên Liệu</label>
		<select id="filterFuel" class="form-select"></select>
	  </div>
	  <div class="col">
		<label for="filterPrice" class="form-label w-100">Đơn Giá</label>
		<select id="filterPrice" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Bồn</label>
		<select id="filterTank" class="form-select"></select>
	  </div>
		<!-- Bốn nút chức năng -->
		<div class="col-auto"><button type="button" class="btn btn-primary form-4control" onclick="fetchLogs()">Làm Mới</button></div>
		<div class="col-auto"><button type="button" class="btn btn-warning form-4control" onclick="sendBatchInvoices()">Xuất Auto</button></div>
		<!--<div class="col-auto"><button type="button" class="btn btn-success form-4control" onclick="exportExcelByDate()">Xuất File</button></div>-->
		<div class="col-auto"><button type="button" class="btn btn-secondary form-4control" onclick="markInternalLogs()">Nội Bộ</button></div>
		<!-- <div class="col-auto"><button id="autoToggleBtn" class="btn btn-danger form-4control" onclick="toggleAutoSend(true)">Auto OFF</button></div> -->
    </div>

    <!-- Thống kê tổng tiền, tổng lít... -->
    <div id="logSummary" class="summary-bar"></div>

    <!-- Bảng log -->
    <div class="table-wrapper">
      <table id="logTable" class="table table-bordered table-hover">
        <thead>
          <tr>
		    <th><input type="checkbox" id="selectAll" onclick="toggleSelectAll(this)"/></th>
            <th>NGÀY</th>
            <th class="sortable" onclick="sortTable(this)">GIỜ<i class="fa-solid fa-sort"></i></th>
            <th>CÒ</th>
            <th>NHIÊN LIỆU</th>
			<th class="sortable" onclick="sortTable(this)">SỐ TIỀN<i class="fa-solid fa-sort"></i></th>
            <th class="sortable" onclick="sortTable(this)">SỐ LƯỢNG<i class="fa-solid fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(this)">ĐƠN GIÁ<i class="fa-solid fa-sort"></i></th>
            <th class="sortable" onclick="sortTable(this)">HÓA ĐƠN<i class="fa-solid fa-sort"></i></th>
            <th>THAO TÁC</th>
            <th>SERIAL</th>
			<th class="sortable" onclick="sortTable(this)">BỒN<i class="fa-solid fa-sort"></i></th>
            <th>MÃ GIAO DỊCH</th>
			<th>TRẠM</th>
          </tr>
        </thead>
        <tbody id="logBody"></tbody>
      </table>
    </div>
	
    <!-- Phân trang -->
	<div class="pagination-controls d-flex align-items-center justify-content-between mt-2" style="gap: 14px; flex-wrap: wrap; user-select: none;">
	  <select id="logsPerPageSelect" onchange="changeLogsPerPage()" class="form-select" style="width: 80px;">
		<option value="100">100</option>
		<option value="200">200</option>
		<option value="300">300</option>
		<option value="500">500</option>
		<option value="8000" selected>Tất cả</option>
	  </select>
	  
	  <div id="paginationInfo" style="font-size: 14px; color: #444;"></div>
	  
	  <div class="pagination-buttons d-flex align-items-center" style="gap: 4px;">
		<button onclick="goToFirstPage()">«</button>
		<button onclick="goToPrevPage()">‹</button>
		<span id="paginationNumbers"></span>
		<button onclick="goToNextPage()">›</button>
		<button onclick="goToLastPage()">»</button>
	  </div>

	  <div style="display: flex; align-items: center; user-select: none; gap: 8px;">
		 <div id="autoConfigStatus" style="font-size: 14px; color: gray;"></div>
	  </div> 
	</div>
	
  </div>

<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>

<script>
  window.isAdmin = {{ is_admin|tojson }};
</script>

<script>
  window.onload = () => {
    loadDefaultCustomerData();  // gọi ngay khi load trang
  };
</script>

	<!-- Popup xem trước hóa đơn -->
	<div id="xmlPreview" class="modal hidden">
	  <div class="modal-content d-flex flex-row" style="gap: 32px; width: 90vw; max-width: 1600px;">
		<!-- Cột trái: nhập liệu -->
		<div class="invoice-form" style="flex: 1;">
		  <div class="mb-3">
			<input id="searchCustomerPopup" class="form-control"
				   placeholder="🔍 Tìm theo Mã KH, Tên Cty, MST..."
				   oninput="searchCustomerPopup()"
				   onchange="autoFillCustomerByInput()"
				   list="customerList"
				   autocomplete="off" />
			<datalist id="customerList"></datalist>
		  </div>
		  
			<div class="d-flex flex-row gap-2 mb-1">
			  <div style="flex: 1;">
				  <label>Mã khách hàng:</label>
				  <input id="formMKHang" class="form-control" />
				</div>

			  <div style="flex: 1;">
				  <label>Mã ĐVQHNS:</label>
				  <input id="formMdvqhns" class="form-control" />
				</div>
			</div>		  
		  

		  <div class="d-flex flex-row gap-2 mb-1">
			  <div style="flex: 2;">
				<label>Họ tên người mua:</label>
				<input id="formHoTen" class="form-control" />
			  </div>

			  <div style="flex: 1;">
				<label>Số CCCD:</label>
				<input id="formCCCD" class="form-control" />
			  </div>

			  <div style="flex: 1;">
				<label>Số điện thoại:</label>
				<input id="formPhone" class="form-control" />
			  </div>
		  </div>

		  <div class="mb-1">
			<label>Tên đơn vị:</label>
			<input id="formTen" class="form-control" />
		  </div>

		  <div class="mb-1">
			<label>Địa chỉ:</label>
			<input id="formDChi" class="form-control" />
		  </div>
		  <!-- 3 trường nằm cùng hàng --> 
		  <div class="d-flex flex-row gap-2 mb-1">
			  <div style="flex: 1;">
				<label>MST:</label>
				<input id="formMST" class="form-control" />
			  </div>

			  <div style="flex: 1;">
				<label>Thuế suất(%):</label>
				<select id="formTThue" class="form-control">
				  <option value="10">10%</option>
				  <option value="8">8%</option>
				  <option value="5">5%</option>
				  <option value="0">0%</option>
				  <option value="KCT">KCT (Không chịu thuế)</option>
				  <option value="KKKNT">KKKNT (Không khấu trừ)</option>
				</select>
			   </div>
			  
			   <div style="flex: 1;">
				<label>Hình thức thanh toán:</label>
				<select id="formHTTToan" class="form-control">
				  <option value="Tiền mặt/Chuyển khoản">Tiền mặt/Chuyển khoản</option>
				  <option value="Tiền mặt">Tiền mặt</option>
				  <option value="Chuyển khoản">Chuyển khoản</option>
				</select>
			   </div>
			   
			   <div style="flex: 1;">
				  <label>Chiết khấu (đ/lít):</label>
				  <input id="formDiscountPerLit" class="form-control" value="0" />
			   </div>
			  
		   </div>

			<div class="d-flex flex-row gap-2 mb-1">
			  <div style="flex: 1;">
				  <label>Email:</label>
				  <input id="formEmail" class="form-control" />
				</div>

			  <div style="flex: 1;">
				  <label>Biển số xe:</label>
				  <input id="formPlate" class="form-control" />
				</div>
			</div>

		  <div class="d-flex justify-content-between mt-3 gap-2">
			<!-- <button class="btn btn-primary w-100" onclick="searchMST()">Tìm MST</button> -->
			<button class="btn btn-primary w-100" onclick="updatePreview()">Cập Nhật</button>
			<button class="btn btn-warning w-100" onclick="sendInvoiceFromPopup()">Xuất Hóa Đơn</button>
			<button class="btn btn-secondary w-100" onclick="closePreview()">Đóng</button>
		  </div>
		  
			<label style="font-style: italic; color: #2596be;">
			  Khi sửa lại thông tin người mua, thuế suất, v.v.. thì bấm Cập Nhật để áp dụng các thay đổi.
			</label>
		</div>
		
		<!-- Cột phải: hóa đơn preview -->
		<div style="flex: 1;" class="ps-3 border-start">
		  <div id="previewContent" style="margin-top: 0;"></div>
		</div>
	  </div>
	</div>

	<!-- Popup lý do giao dịch nội bộ -->
	<div id="internalNoteModal" class="modal fade" tabindex="-1">
	  <div class="modal-dialog">
		<div class="modal-content">
		  <div class="modal-header bg-secondary text-white text-center d-block w-100">
			  <h5 class="modal-title" style="font-size: 20px; line-height: 1.4;">
				GIẢI TRÌNH LÝ DO TẠO GIAO DỊCH NỘI BỘ.<br>
				<small style="font-size: 16px; font-style: italic;">⚠ Lưu ý: Giao dịch nội bộ chỉ để sửa máy, lường trụ, kiểm định.</small>
			  </h5>
		  </div>
		  <div class="modal-body">
			<textarea id="internalNoteContent" class="form-control" rows="4" placeholder="Nhập lý do nội bộ..." style="font-size: 16px !important;"></textarea>
		  </div>
		  <div class="modal-footer">
		    <button class="btn btn-danger me-auto" onclick="clearInternalNote()">Xóa</button>
			<button class="btn btn-secondary" onclick="closeInternalNotePopup()">Đóng</button>
			<button class="btn btn-primary" onclick="saveInternalNote()">Lưu</button>
		  </div>
		</div>
	  </div>
	</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script src="{{ url_for('static', filename='invoice_providers/vnpt.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/mobifone.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/mobifone_test.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/bkav.js') }}"></script>
<script src="{{ url_for('static', filename='invoice_providers/viettel.js') }}"></script>
<script src="{{ url_for('static', filename='main.js') }}"></script>

<script>
  function showModal(id) {
    document.getElementById(id)?.classList.add("show");
  }
  function hideModal(id) {
    document.getElementById(id)?.classList.remove("show");
  }
  function closePreview() {
    document.getElementById("xmlPreview")?.classList.remove("show");
    window.previewData = null;
    loadDefaultCustomerData();
    document.getElementById("previewContent").innerHTML = "";
  }
  // Gán global
  window.showModal = showModal;
  window.hideModal = hideModal;
  window.closePreview = closePreview;
</script>


<script>
// 👂 Lắng nghe nếu tab khác (setting.html, config_invoice.html, bank_setting.html) vừa lưu
window.addEventListener("storage", function(e) {
  const keysToWatch = [
    "settingsUpdatedAt",
    "configInvoiceUpdatedAt",
    "bankSettingUpdatedAt",
    "logsResetAt"
  ];

  if (keysToWatch.includes(e.key)) {
	if (window.isInvoiceProcessing || window.isBatchSending) {
      console.warn("⛔ Đang xuất hóa đơn – chặn reload từ tab khác.");
      return; // Chặn reload khi đang xuất
    }

    let title = "Cập nhật hệ thống";
    let text = "Thông tin đã thay đổi. Đang làm mới...";

    if (e.key === "settingsUpdatedAt") {
      title = "Đã cập nhật cài đặt";
      text = "Thông tin cài đặt mặc định đã được áp dụng.";
    }
    if (e.key === "configInvoiceUpdatedAt") {
      title = "Đã cập nhật cấu hình hóa đơn";
      text = "Cấu hình hóa đơn đã được áp dụng.";
    }
    if (e.key === "bankSettingUpdatedAt") {
      title = "Đã cập nhật tài khoản ngân hàng";
      text = "Thông tin ngân hàng đã được áp dụng.";
    }
    if (e.key === "logsResetAt") {
      title = "Đã reset giao dịch xuất hóa đơn";
      text = "Hệ thống sẽ tải lại danh sách GD mới nhất.";
    }

    Swal.fire({
      icon: "info",
      title,
      text,
      timer: 2000,
      showConfirmButton: false,
      willClose: () => {
        location.reload();
      }
    });
  }
});

</script>
</body>
</html>
