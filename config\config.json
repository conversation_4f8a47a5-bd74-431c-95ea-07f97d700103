{"provider": "BKAV", "VNPT": {"Account": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACpass": "Einv@oi@vn#pt25", "username": "satracorpservice", "password": "Einv@oi@vn#pt25", "link_api": "https://satracorp-tt78admindemo.vnpt-invoice.com.vn/PublishService.asmx", "link_api_fkey": "https://satracorp-tt78admindemo.vnpt-invoice.com.vn/PortalService.asmx", "pattern": "1/002", "serial": "C25MAT", "convert": "0", "invoice_type": "", "NoDigitofPrice": "", "sellerFullName": "CHXD SỐ 39", "sellerName": "TỔNG CÔNG TY THƯƠNG MẠI SÀI GÒN - TNHH MỘT THÀNH VIÊN", "sellerTaxCode": "**********", "sellerAddress": "275 B <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, VN"}, "MOBIFONE": {"tax_code": "**********", "username": "THIENTRAN", "password": "Psp@123", "template_code": "1C25MMT", "login_url": "http://mobiinvoice.vn:9000/api/Account/Login", "get_cctbao_url": "http://mobiinvoice.vn:9000/api/System/GetDataReferencesByRefId", "link_api": "http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78", "link_api_fkey": "http://mobiinvoice.vn:9000/api/Invoice68/GetHoadonFkey", "pdf_url": "http://mobiinvoice.vn:9000/api/Invoice68/inHoadon", "sellerFullName": "CHXD SỐ 39", "sellerName": "TỔNG CÔNG TY THƯƠNG MẠI SÀI GÒN - TNHH MỘT THÀNH VIÊN", "sellerTaxCode": "**********", "sellerAddress": "275 B <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, VN"}, "MOBIFONE_TEST": {"tax_code": "**********-998", "username": "55556666TEST", "password": "jdlsOUj98IQ=", "ma_dvcs": "", "login_url": "http://mobiinvoice.vn:9000/api/Account/Login", "get_cctbao_url": "", "link_api": "http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78", "generate_url": "http://mobiinvoice.vn:9000/api/Invoice68/GenerateInvoiceNumber", "cctbao_id": "d9b808ad-f9f1-4487-81a3-************", "template_code": "", "sign_mode": "", "invoice_type": "", "NoDigitofPrice": "", "sellerFullName": "CHXD SỐ 39", "sellerName": "TỔNG CÔNG TY THƯƠNG MẠI SÀI GÒN - TNHH MỘT THÀNH VIÊN", "sellerTaxCode": "**********", "sellerAddress": "275 B <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, VN"}, "BKAV": {"partnerGUID": "b3b286b3-9a2d-4544-bbfa-233d0bbec4df", "partnerToken": "JW4g8p3R8UP0LSoWjYJOuAC1nuruKGj5r0WfFegd89A=:wEm2jfUYa5wtBUYuFdX4Ag==", "env_testing": "ON", "url_api_link": "https://ws.ehoadon.vn/WSPublicEHoaDon.asmx", "url_api_link_test": "https://wsdemo.ehoadon.vn/WSPublicEHoaDon.asmx/ExecCommand", "url_link_PDF": "https://demo.ehoadon.vn", "cmd_type": "101", "invoice_type_id": "1", "currency_id": "VND", "exchange_rate": "1", "no_digit_of_price": "4", "sellerFullName": "CHXD SỐ 39", "sellerName": "TỔNG CÔNG TY THƯƠNG MẠI SÀI GÒN - TNHH MỘT THÀNH VIÊN", "sellerTaxCode": "**********", "sellerAddress": "275 B <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, VN"}, "VIETTEL": {"tax_code": "0100109106-507", "username": "0100109106-507", "password": "2wsxCDE#", "url_api_link": "https://api-vinvoice.viettel.vn/services/einvoiceapplication/api/InvoiceAPI/InvoiceWS/createInvoice", "env_testing": "OFF", "invoice_type": "1", "template_code": "1/395", "invoice_series": "C25MTC", "exchange_rate": "1", "validation": "0", "no_digit_of_price": "4", "sellerFullName": "CHXD SỐ 39", "sellerName": "TỔNG CÔNG TY THƯƠNG MẠI SÀI GÒN - TNHH MỘT THÀNH VIÊN", "sellerTaxCode": "**********", "sellerAddress": "275 B <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, VN"}}