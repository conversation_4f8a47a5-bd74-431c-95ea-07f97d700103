<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title><PERSON><PERSON><PERSON><PERSON> l<PERSON> g<PERSON>o d<PERSON>ch</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
	.summary-bar {
	margin-top: 10px;
	margin-bottom: 15px;
	font-weight: bold;
	font-size: 14px;
	background-color: #f8f9fa;
	padding: 8px 12px;
	border: 1px solid #dee2e6;
	border-radius: 6px;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 10px;
	}
	.modal {
	position: fixed;
	top: 0; left: 0;
	width: 100vw; height: 100vh;
	background-color: rgba(0,0,0,0.5);
	align-items: center;
	justify-content: center;
	display: none; /* mặc định ẩn */
	}

	.modal.show {
	display: flex !important; /* khi có class .show thì hiển thị */
	}

	.close {
	float: right;
	font-size: 24px;
	cursor: pointer;
	}
	.modal-content {
	background-color: white;
	padding: 20px;
	border-radius: 10px;
	width: 800px;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 5px 15px rgba(0,0,0,0.3);
	}
	th, td {
	text-align: center;
	vertical-align: middle !important;
	white-space: normal !important;
	word-break: break-word; /* Cho phép xuống dòng khi cần */
	}


  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body class="d-flex flex-column min-vh-100">
  <div class="container-fluid px-3 flex-grow-1">
	<div class="header-row d-flex align-items-center justify-content-between flex-wrap">
	  <!-- Logo và tên công ty -->
	  <div class="d-flex align-items-center gap-2">
		<img src="/static/favicon.ico" alt="Logo" style="height: 40px; border-radius: 8px; cursor: pointer;" onclick="location.reload()" />
		<div>
		  <h2 id="sellerName" class="m-0" style="color:  #dc3545; font-weight: bold; font-size: 20px;">CÔNG TY MONTECH</h2>
			<p class="m-0" style="font-size: 12px; font-weight: bold; color: #063970; white-space: nowrap;">
			  <span id="sellerTaxcode">**********</span>
			  <span id="sellerFullName">Montech</span>
			  <span id="providerLabel" class="text-muted ms-1" style="font-weight: normal;"></span>
			</p>
		</div>
	  </div>

	  <!-- Phần bên phải: Cập nhật + Cài đặt + User -->
	  <div class="d-flex align-items-center gap-3">
		<!-- ✅ Trạng thái cập nhật -->
		<div id="fetchStatus" style="font-size: 14px; color: gray; text-align: center;"></div>
		<div id="activeUserCount" style="font-size: 14px; color: gray; text-align: center;"></div>		
	  </div>
	  
	</div>
    <div style="height: 1px; background-color: #ccc; margin: 6px 0;"></div>
	
    <!-- Bộ lọc -->
    <div class="row g-1 align-items-end mb-1 text-center">
	  <div class="col">
		<label class="form-label w-100">Từ Ngày</label>
		<input type="date" id="filterStartDate" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Đến Ngày</label>
		<input type="date" id="filterEndDate" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Từ Giờ</label>
		<input type="time" id="filterStartTime" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Đến Giờ</label>
		<input type="time" id="filterEndTime" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Cò</label>
		<select id="filterFaucet" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Nhiên liệu</label>
		<select id="filterFuel" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Đơn giá</label>
		<select id="filterPrice" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Mã GD</label>
		<input type="text" id="filterCode" class="form-control">
	  </div>
	  <div class="col-auto" style="display: none;">
		  <div class="form-check">
			<input class="form-check-input" type="checkbox" id="filterIsInvoiced" checked>
			<label class="form-check-label" for="filterIsInvoiced">
			  GD đã có hóa đơn
			</label>
		  </div>
	  </div>

	  <div class="col-auto">
		<button class="btn btn-success btn-sm" onclick="updateInvoiceNumbers()">Gán số HĐ</button>
	  </div>
	  
	  <div class="col-auto">
		<button class="btn btn-danger btn-sm" onclick="resetSelectedInvoices()">Xóa số HĐ</button>
	  </div>
		
	  <div class="col-auto">
		<button class="btn btn-danger btn-sm" onclick="resetSelectedQR()">Xóa CK</button>
	  </div>
	  
	  <div class="col-auto">
		<button class="btn btn-primary btn-sm" onclick="showImportCSVOptions()">Import GD</button>
	  </div>
	  
	  <div class="col-auto">
	    <button class="btn btn-secondary btn-sm" onclick="deleteLogsBySelectedDate()">Reset GD</button>
	  </div>
	  
	  <div class="col-auto">
		<button class="btn btn-warning btn-sm" onclick="showIPWAN()">IP WAN</button>
	  </div>
	  
	  <div class="col-auto">
		<button class="btn btn-info btn-sm" onclick="compareLogs()">DB-CSV</button>
	  </div>

	</div>

	<div class="d-flex flex-wrap gap-2 mb-2" style="align-items: start;">
	  <div id="logSummary" class="flex-fill"></div>
	  <div id="invoiceStats" class="flex-fill"></div>
	</div>

    <div class="table-wrapper">
      <table id="logTable" class="table table-bordered table-hover">
		<thead>
		  <tr>
			<th><input type="checkbox" id="select-all" onchange="toggleSelectAll(this)"></th>
			<th class="sortable" onclick="sortTable(this)">NGÀY <i class="fa fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(this)">GIỜ <i class="fa fa-sort"></i></th>
			<th>CÒ</th>
			<th>NHIÊN LIỆU</th>
			<th class="sortable" onclick="sortTable(this)">SỐ TIỀN <i class="fa fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(this)">SỐ LÍT <i class="fa fa-sort"></i></th>
			<th>ĐƠN GIÁ</th>
			<th class="sortable" onclick="sortTable(this)">SỐ HĐ <i class="fa fa-sort"></i></th>
			<th>QR</th>
			<th>SERIAL</th>
			<th>BỒN</th>			
			<th>MÃ GD</th>
			<th>TRẠM</th>
		  </tr>
		</thead>
        <tbody id="logResetBody"></tbody>
      </table>
    </div>
	
  </div>

<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>

<script>
//---------------------------------------------------------------------------------------------------
function toVNDate(dateStr) {
	if (!dateStr || dateStr.indexOf("-") === -1) return dateStr;
	const dArr = dateStr.split("-");
	return `${dArr[2]}-${dArr[1]}-${dArr[0]}`;
}

//---------------------------------------------------------------------------------------------------
function formatVNDateTime(dateTimeStr) {
  if (!dateTimeStr.includes("T") && !dateTimeStr.includes(" ")) return dateTimeStr;
  const dt = new Date(dateTimeStr);
  if (isNaN(dt)) return dateTimeStr;

  const pad = n => n.toString().padStart(2, "0");
  const dd = pad(dt.getDate());
  const mm = pad(dt.getMonth() + 1);
  const yyyy = dt.getFullYear();
  const hh = pad(dt.getHours());
  const mi = pad(dt.getMinutes());
  const ss = pad(dt.getSeconds());

  return `${dd}-${mm}-${yyyy} ${hh}:${mi}:${ss}`;
}

//---------------------------------------------------------------------------------------------------
let fuelMap = {};
async function loadFuelMap() {
  try {
    const res = await fetch("/fuel_map");
    fuelMap = await res.json();
  } catch (e) {
    console.error("❌ Lỗi khi load fuelMap:", e);
    fuelMap = {};
  }
}

//---------------------------------------------------------------------------------------------------
function getMappedFuelName(code) {
  return fuelMap?.[code] || code || "";
}


//---------------------------------------------------------------------------------------------------
let tankMap = {};
async function loadTankMap() {
  try {
    const res = await fetch("/tank_map");
    tankMap = await res.json();
  } catch (e) {
    console.error("❌ Lỗi khi load tankMap:", e);
    tankMap = {};
  }
}

//---------------------------------------------------------------------------------------------------
function getMappedTankName(serial) {
  return tankMap?.[serial] || "";
}

// Nút trạng thái đang cập nhật log
function updateFetchStatus(status) {
  const el = document.getElementById("fetchStatus");
  if (!el) return;
  el.innerText = status;
}

//---------------------------------------------------------------------------------------------------
function loadResetLogs() {
  updateFetchStatus("🟢 Đang tải GD...");

  const start = document.getElementById("filterStartDate")?.value;
  const end = document.getElementById("filterEndDate")?.value;

  fetch(`/logs?start=${start}&end=${end}`)  // ✅ Có start & end
    .then(res => res.json())
    .then(data => {
      currentLogs = data.logs;
      applyResetFilters();

      const now = new Date().toLocaleTimeString('vi-VN', { hour12: false });
      updateFetchStatus(`✅ Cập nhật ${now}`);
    })
    .catch(err => {
      console.error("❌ Lỗi tải logs:", err);
      updateFetchStatus("❌ Không thể tải logs");
    });
}

//---------------------------------------------------------------------------------------------------
function fillSelect(id, values) {
  const select = document.getElementById(id);
  const current = select.value;
  select.innerHTML = '<option value="">Tất cả</option>';

  [...values].sort().forEach(val => {
    const label = (id === "filterFuel") ? (fuelMap[val] || val) : val;
    const text = typeof val === "number" ? val.toLocaleString('vi-VN') : label;
    select.innerHTML += `<option value="${val}">${text}</option>`;
  });

  select.value = current;
}

//---------------------------------------------------------------------------------------------------
function updateResetDropdowns(logs) {
  const selectedFaucet = document.getElementById("filterFaucet").value;
  const selectedFuel = document.getElementById("filterFuel").value;
  const selectedPrice = document.getElementById("filterPrice").value;

  const faucets = new Set();
  const fuels = new Set();
  const prices = new Set();

  for (let log of logs) {
    const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
    const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
    const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;

    if (matchFuel && matchPrice) faucets.add(log.faucetNo);
    if (matchFaucet && matchPrice) fuels.add(log.fuelType);
    if (matchFuel && matchFaucet) prices.add(log.transactionPrice);
  }

  fillSelect("filterFaucet", faucets);
  fillSelect("filterFuel", fuels);
  fillSelect("filterPrice", prices);
}
//---------------------------------------------------------------------------------------------------
function applyResetFilters() {
	updateResetDropdowns(currentLogs); 
	const onlyInvoiced = document.getElementById("filterIsInvoiced").checked;
	const startDate = document.getElementById("filterStartDate").value;
	const endDate = document.getElementById("filterEndDate").value;
	const startTime = document.getElementById("filterStartTime").value;
	const endTime = document.getElementById("filterEndTime").value;
	const faucet = document.getElementById("filterFaucet").value;
	const fuel = document.getElementById("filterFuel").value;
	const price = document.getElementById("filterPrice").value;
	const code = document.getElementById("filterCode").value.toLowerCase();


	const filtered = currentLogs.filter(log => {
		if (onlyInvoiced) {
			const inv = log.invoiceNumber;
			if (!inv || inv === "Nội bộ" || inv === "CHUA_RO_SOHĐ" || inv === "0") return false;
			}
		if (startDate && log.transactionDate < startDate) return false;
		if (endDate && log.transactionDate > endDate) return false;
		if (startTime && log.transactionTime < startTime) return false;
		if (endTime && log.transactionTime > endTime) return false;
		if (faucet && String(log.faucetNo) !== faucet) return false;
		if (fuel && log.fuelType !== fuel) return false;
		if (price && String(log.transactionPrice) !== price) return false;
		if (code) {
		  const costStr = String(log.transactionCost).replace(/[.,]/g, '').toLowerCase();
		  const codeStr = log.transactionCode.toLowerCase();
		  if (!codeStr.includes(code) && !costStr.includes(code)) return false;
		}
		return true;

});

renderFilteredResetLogs(filtered);
}

//---------------------------------------------------------------------------------------------------
function renderFilteredResetLogs(logs) {
  window.currentLogs = logs;  //Cần thiết để generateQR dùng được
  logs.sort((a, b) => {
    const n1 = parseInt(a.invoiceNumber) || 0;
    const n2 = parseInt(b.invoiceNumber) || 0;
    return n2 - n1;
  });

  const totalInternal = logs.filter(l => l.invoiceNumber === "Nội bộ").length;
  const totalNonInternal = logs.length - totalInternal;

  const tbody = document.getElementById("logResetBody");
  const summary = document.getElementById("logSummary");
  const statsDiv = document.getElementById("invoiceStats");
  tbody.innerHTML = '';

  let sumCost = 0, sumLitre = 0;
  logs.forEach(log => {
    sumCost += log.transactionCost;
    sumLitre += log.transactionAmount / 1000;
  });

  const invoiceObjects = logs
    .map(l => l.invoiceNumber)
    .filter(x => typeof x === "string" && x.trim() !== "" && x !== "CHUA_RO_SOHĐ" && x !== "Nội bộ")
    .map(x => ({
      original: x,
      sortKey: parseInt(x.split('_')[0]) || 0
    }))
    .sort((a, b) => a.sortKey - b.sortKey);

  const invoiceNumbers = invoiceObjects.map(x => x.original);
  const hasUnderscore = invoiceNumbers.some(inv => inv.includes("_"));

  const faucet = document.getElementById("filterFaucet").value;
  const fuel = document.getElementById("filterFuel").value;
  const price = document.getElementById("filterPrice").value;
  const code = document.getElementById("filterCode").value.trim();
  const isStrictFilter = faucet || fuel || price || code;

  let minInvoice = null, maxInvoice = null, missingCount = 0, missingList = [];

  if (isStrictFilter) {
    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary" style="font-weight: bold;">Hiển thị GD đã có hóa đơn</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-danger">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label text-danger">Đang lọc, bỏ qua kiểm tra thiếu số HĐ</span>
      </div>
    `;
  } else if (!hasUnderscore && invoiceObjects.length > 0) {
    minInvoice = invoiceObjects[0].sortKey;
    maxInvoice = invoiceObjects[invoiceObjects.length - 1].sortKey;
    const fullSet = new Set(invoiceObjects.map(x => x.sortKey));
    const maxGap = maxInvoice - minInvoice;

    if (maxGap < 5000) {
      for (let i = minInvoice; i <= maxInvoice; i++) {
        if (!fullSet.has(i)) {
          missingCount++;
          missingList.push(i);
        }
      }
    }

    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Số HĐ nhỏ nhất:</span> <span class="stat-number text-success">${minInvoice}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Số HĐ lớn nhất:</span> <span class="stat-number text-success">${maxInvoice}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Thiếu:</span> <span class="stat-number text-danger">${missingCount}</span>
        ${missingList.length > 0 ? `<br/><span class="label">Danh sách thiếu:</span> <span class="stat-number text-danger">${missingList.join(", ")}</span>` : ""}
      </div>
    `;
  } else {
    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label text-danger">Không kiểm tra thiếu do số HĐ phức tạp</span>
      </div>
    `;
  }

  summary.innerHTML = `
    <div class="d-flex flex-wrap justify-content-between align-items-center border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
      <div>
        <span class="label">Tổng tiền:</span> <span class="stat-number text-primary">${sumCost.toLocaleString('vi-VN')}</span> đồng
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng lít:</span> <span class="stat-number text-primary">${sumLitre.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</span> lít
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng GD:</span> <span class="stat-number text-primary">${logs.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Chưa xuất:</span> <span class="stat-number text-danger">${totalNonInternal - invoiceNumbers.length}</span>		
        &nbsp;&nbsp;&nbsp;
        <span class="label">Nội bộ:</span> <span class="stat-number text-secondary">${totalInternal}</span>
		&nbsp;&nbsp;&nbsp;
		<span class="label">Đã CK:</span> <span class="stat-number text-success">${logs.filter(log => log.qr_paid).length}</span>
      </div>
    </div>
  `;

  document.getElementById("filterIsInvoicedSummary").addEventListener("change", (e) => {
    document.getElementById("filterIsInvoiced").checked = e.target.checked;
    applyResetFilters();
  });

  // Bảng log
  for (let log of logs) {
    const tr = document.createElement("tr");
    tr.innerHTML = `
      <td><input type="checkbox" class="row-checkbox" value="${log.transactionCode}" /></td>
      <td>${toVNDate(log.transactionDate)}</td>
      <td>${log.transactionTime}</td>
      <td>${log.faucetNo}</td>
      <td>${getMappedFuelName(log.fuelType)}</td>		
      <td>${log.transactionCost.toLocaleString('vi-VN')}</td>
      <td>${(log.transactionAmount / 1000).toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</td>
      <td>${log.transactionPrice.toLocaleString('vi-VN')}</td>
		<td>
		  ${
			log.invoiceNumber === "Nội bộ"
			  ? `<span class="badge bg-secondary text-light rounded-pill px-3 py-1">Nội bộ</span>`
				: log.invoiceNumber && log.invoiceNumber !== "CHUA_RO_SOHĐ" && log.invoiceNumber !== "0"
				  ? `<span class="badge bg-success rounded-pill px-3 py-1" style="font-size: 12px;">${log.invoiceNumber}</span>`
				: `<input type="text" class="form-control form-control-sm invoice-input text-center"
						  data-code="${log.transactionCode}" 
						  value="${log.invoiceNumber || ''}" 
						  placeholder="Nhập số HĐ">`
		  }
		</td>
	  <td>
		<i class="fa-solid fa-qrcode ${log.qr_paid ? 'text-success' : 'text-danger'}"
		style="cursor:pointer" onclick='generateQR("${log.transactionCode}")'></i>
	  </td>
	  <td>${log.pumpSerial}</td>
	  <td>${getMappedTankName(log.pumpSerial)}</td>	  
      <td>${log.transactionCode}</td>
      <td>${log.stationCode || ''}</td>
    `;
    tbody.appendChild(tr);
  }
}

//---------------------------------------------------------------------------------------------------
function toggleSelectAll(master) {
	const checkboxes = document.querySelectorAll(".row-checkbox");
	checkboxes.forEach(cb => cb.checked = master.checked);
}
//---------------------------------------------------------------------------------------------------
async function resetSelectedInvoices() {
  const selected = Array.from(document.querySelectorAll('.row-checkbox:checked'));
  if (!selected.length) {
    Swal.fire("", "Vui lòng chọn ít nhất một giao dịch.", "warning");
    return;
  }

  const allCodes = selected.map(cb => cb.value);
  const codes = allCodes.filter(code => {
    const log = window.currentLogs.find(l => l.transactionCode === code);
    return log && log.invoiceNumber;
  });

  if (!codes.length) {
    Swal.fire("", "Không có log nào đã xuất hóa đơn để reset.", "info");
    return;
  }

  const result = await Swal.fire({
    icon: "question",
    title: "Xác nhận Reset",
    html: `Bạn sắp <b>RESET</b> <span style="color: red;">${codes.length}</span> giao dịch đã xuất hóa đơn.`,
    showCancelButton: true,
    confirmButtonText: "Đồng ý",
    cancelButtonText: "Hủy",
    confirmButtonColor: "#d33",
    cancelButtonColor: "#6c757d"
  });

  if (!result.isConfirmed) return;

  try {
    await Promise.all(
      codes.map(code =>
        fetch('/api/check_invoice', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ transactionCode: code })
        })
      )
    );

    Swal.fire("Đã reset thành công!", "", "success").then(() => {
      const timestamp = Date.now().toString();
      localStorage.setItem("logsResetAt", timestamp);
      window.dispatchEvent(new StorageEvent("storage", {
        key: "logsResetAt",
        newValue: timestamp
      }));
      location.reload();
    });
  } catch (err) {
    console.error(err);
    Swal.fire("❌ Có lỗi xảy ra khi reset.", "", "error");
  }
}


//---------------------------------------------------------------------------------------------------
async function resetSelectedQR() {
  const selected = Array.from(document.querySelectorAll('.row-checkbox:checked'));
  if (!selected.length) {
    Swal.fire("", "Vui lòng chọn ít nhất một giao dịch.", "warning");
    return;
  }

  const allCodes = selected.map(cb => cb.value);
  const codes = allCodes.filter(code => {
    const log = window.currentLogs.find(l => l.transactionCode === code);
    return log && log.qr_paid === true;
  });

  if (!codes.length) {
    Swal.fire("", "Không có log nào cần xóa QR.", "info");
    return;
  }

  const result = await Swal.fire({
    icon: "question",
    title: "Xác nhận xóa QR",
    html: `Bạn sắp xóa QR của <b style="color:red;">${codes.length}</b> giao dịch.`,
    showCancelButton: true,
    confirmButtonText: "Đồng ý",
    cancelButtonText: "Hủy",
    confirmButtonColor: "#d33"
  });

  if (!result.isConfirmed) return;

  try {
    await Promise.all(
      codes.map(code =>
        fetch('/api/unmark_qr_paid', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ transactionCode: code })
        })
      )
    );

    Swal.fire("Đã xóa QR thành công", "", "success");
    await loadResetLogs();
  } catch (err) {
    console.error(err);
    Swal.fire("❌ Có lỗi xảy ra khi xóa QR", "", "error");
  }
}


//---------------------------------------------------------------------------------------------------
function updateInvoiceNumbers() {
	const inputs = document.querySelectorAll('.invoice-input');
	const updates = [];

	inputs.forEach(input => {
	const code = input.dataset.code;
	const invoice = input.value.trim();
	if (invoice) {
	  updates.push({ code, invoice });
	}
	});

	if (!updates.length) {
	alert("Không có số HĐ nào được nhập.");
	return;
	}

	if (!confirm(`Bạn có chắc muốn gán lại ${updates.length} số HĐ?`)) return;

	Promise.all(
	updates.map(item => 
	  fetch('/api/assign_invoice_number', {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify({
		  transactionCode: item.code,
		  invoiceNumber: item.invoice
		})
	  })
	)
	).then(() => {
	alert("Đã gán lại số hóa đơn.");
	loadResetLogs();
	});
}
//---------------------------------------------------------------------------------------------------
function sortTable(thElement) {
	const table = thElement.closest("table");
	const tbody = table.querySelector("tbody");
	const rows = Array.from(tbody.querySelectorAll("tr"));

	// Tính colIndex động theo vị trí thực tế của th
	const headers = Array.from(thElement.parentElement.children);
	const colIndex = headers.indexOf(thElement);

	const asc = table.dataset.sortCol == colIndex && table.dataset.sortDir !== "asc";
	table.dataset.sortCol = colIndex;
	table.dataset.sortDir = asc ? "asc" : "desc";

	const parseMoney = (str) => parseFloat(str.replace(/\./g, '').replace(/,/g, '.')) || 0;
	const parseLit = (str) => parseFloat(str.replace(",", ".")) || 0;
	const parseTime = (str) => {
		const parts = str.split(":").map(Number);
		return parts.length === 3 ? parts[0] * 3600 + parts[1] * 60 + parts[2] : 0;
	};

	rows.sort((a, b) => {
		let v1 = a.cells[colIndex]?.textContent.trim() || '';
		let v2 = b.cells[colIndex]?.textContent.trim() || '';

		let n1 = v1, n2 = v2;

		if (v1.includes("₫") || v1.match(/^\d{1,3}(\.\d{3})*(,\d+)?$/)) {
			n1 = parseMoney(v1);
			n2 = parseMoney(v2);
		} else if (v1.match(/^\d+,\d+$/) || v1.match(/^\d{1,3}(,\d{3})*$/)) {
			n1 = parseLit(v1);
			n2 = parseLit(v2);
		} else if (v1.match(/^\d{1,2}:\d{2}:\d{2}$/)) {
			n1 = parseTime(v1);
			n2 = parseTime(v2);
		} else {
			n1 = v1.toLowerCase();
			n2 = v2.toLowerCase();
		}

		if (typeof n1 === "string") {
			return asc ? n1.localeCompare(n2) : n2.localeCompare(n1);
		} else {
			return asc ? n1 - n2 : n2 - n1;
		}
	});

	tbody.innerHTML = "";
	rows.forEach(row => tbody.appendChild(row));
}

//---------------------------------------------------------------------------------------------------
function getTodayVN() {
	const now = new Date();
	now.setHours(now.getHours() + 7);
	return now.toISOString().slice(0, 10);
}
//---------------------------------------------------------------------------------------------------
function setDefaultDates() {
  const todayStr = getTodayVN();
  document.getElementById("filterStartDate").value = todayStr;
  document.getElementById("filterEndDate").value = todayStr;

  // Bỏ tích mặc định ô "Hiển thị GD đã có hóa đơn"
  document.getElementById("filterIsInvoiced").checked = false;
}

//---------------------------------------------------------------------------------------------------
function showImportCSVOptions() {
  Swal.fire({
    title: "CHỌN CÁCH IMPORT",
    html: `
      <p>File sẽ import theo ngày đang chọn:</p>
      <button id="btnImportFromMonBox" class="btn btn-primary w-100 mb-2">🌐 Tải file từ link Noip MonBox</button>
      <button id="btnImportFromCompareLink" class="btn btn-warning w-100 mb-2">📥 Tải file từ IP WAN MonBox </button>
      <hr>
      <input type="file" id="csvFileInput" accept=".csv" class="form-control mb-2" />
      <button id="btnImportFromFile" class="btn btn-secondary w-100">📁 Tải file từ thư mục</button>
    `,
    showConfirmButton: false,
    didOpen: () => {
      document.getElementById("btnImportFromMonBox").onclick = () => {
        Swal.close();
        importCSVByDate();
      };

      document.getElementById("btnImportFromCompareLink").onclick = () => {
        Swal.close();
        importFromCompareLink(); // Gọi hàm tự động import từ link so sánh
      };

      document.getElementById("btnImportFromFile").onclick = () => {
        const file = document.getElementById("csvFileInput").files[0];
        if (!file) {
          Swal.fire("", "Vui lòng chọn file CSV", "warning");
          return;
        }
        importCSVFromFile(file);
      };
    }
  });
}


//---------------------------------------------------------------------------------------------------
async function importCSVByDate() {
  const date = document.getElementById("filterStartDate").value;
  if (!date) {
    Swal.fire("", "Vui lòng chọn ngày", "warning");
    return;
  }

  // Chống bấm liên tục trong vòng 30s
  const now = Date.now();
  const lastImport = localStorage.getItem("lastImportLogByDate");
  if (lastImport && now - parseInt(lastImport) < 30_000) {
    Swal.fire("⏳", "Bạn vừa import. Vui lòng chờ 30 giây rồi thử lại.", "info");
    return;
  }
  localStorage.setItem("lastImportLogByDate", now);

  try {
    const res = await fetch("/api/import_log_by_date", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ date })
    });

    if (!res.ok) {
      throw new Error(`Lỗi HTTP ${res.status}`);
    }

    const json = await res.json();
    if (json.success) {
      Swal.fire("", `Đã import: ${json.inserted}, Bỏ qua (đã có): ${json.skipped}`, "success");
      loadResetLogs();
    } else {
      Swal.fire("❌", json.message || "Import thất bại", "error");
    }

  } catch (err) {
    console.error("❌ Lỗi import từ MonBox:", err);
    Swal.fire({
      icon: "error",
	  html: `
		<div style="font-size: 16px;">
		  Không thể truy cập thiết bị MonBox.<br><br>
		  <b>Vui lòng thực hiện:</b>
		  <ul style="text-align:left; margin-top:5px;">
			<li>Tắt modem mạng và thiết bị MonBox</li>
			<li>Chờ 5 phút → Bật modem, rồi bật MonBox</li>
			<li>Đợi khoảng 3 phút → Tải lại trang (F5)</li>
		  </ul>
		</div>
	  `,
      timer: 15000,
      confirmButtonText: "OK",
      confirmButtonColor: "#6c63ff"
    });
  }
}


//---------------------------------------------------------------------------------------------------
async function importCSVFromFile(file) {
  const formData = new FormData();
  formData.append("file", file);

  const res = await fetch("/api/import_csv_file", {
    method: "POST",
    body: formData
  });

  const json = await res.json();
  if (json.success) {
    Swal.fire("Đã import file", `Ghi: ${json.inserted}, Bỏ qua: ${json.skipped}`, "success");
    loadResetLogs();
  } else {
    Swal.fire("❌", json.message || "Import thất bại", "error");
  }
}

//---------------------------------------------------------------------------------------------------
async function validateDateRange_Check(showPopup = true) {
  const startInput = document.getElementById('filterStartDate');
  const endInput = document.getElementById('filterEndDate');

  const start = new Date(startInput.value);
  const end = new Date(endInput.value);

  if (!start || !end || isNaN(start) || isNaN(end)) return false;

  const diffMs = end.getTime() - start.getTime();
  const diffDays = diffMs / (1000 * 60 * 60 * 24);

  if (diffDays > 92) {
    await Swal.fire({
      icon: 'warning',
      title: 'Khoảng thời gian quá dài!',
      html: `Bạn chỉ được chọn tối đa <b>90 ngày</b>.<br>Hiện tại đang chọn <b>${diffDays.toFixed(0)}</b> ngày.`,
      confirmButtonText: 'OK'
    });
    return false;
  }

  if (diffDays > 0 && showPopup) {
    await Swal.fire({
      icon: 'info',
      html: `Bạn đang chọn <b>${diffDays.toFixed(0)}</b> ngày.`,
      confirmButtonText: 'OK'
    });
  }

  return true;
}

//---------------------------------------------------------------------------------------------------
async function checkMissingLogs() {
  const start = document.getElementById("filterStartDate").value;
  const end = document.getElementById("filterEndDate").value;

  if (!start || !end) {
    Swal.fire("", "Vui lòng chọn khoảng thời gian", "warning");
    return;
  }

  try {
    const res = await fetch(`/api/check_missing_logs?start=${start}&end=${end}`);
    const json = await res.json();

    if (!json.success) {
      Swal.fire("", json.message || "Lỗi kiểm tra", "error");
      return;
    }

    const data = json.data;
    if (!data.length) {
      Swal.fire("", "Không phát hiện thiếu giao dịch.", "success");
      return;
    }

    let html = "";
    for (let item of data) {
      html += `
        <div style="margin-bottom: 10px;">
          <b>Cò ${item.faucet || "?"} - Serial ${item.serial}</b> (${toVNDate(item.date)})<br/>
          Thiếu PumpNo (${item.total_missing}) log: <span class="text-danger">${item.missing.join(", ")}</span>
        </div>
      `;
    }

    Swal.fire({
      icon: "warning",
      title: `Thiếu giao dịch từ ${toVNDate(start)} đến ${toVNDate(end)}`,
      html,
      width: 700
    });

  } catch (err) {
    console.error("❌ checkMissingLogs() error:", err);
    Swal.fire("❌ Lỗi", err.message || "Không thể kiểm tra thiếu log", "error");
  }
}

//---------------------------------------------------------------------------------------------------
async function deleteLogsBySelectedDate() {
  const date = document.getElementById("filterStartDate").value;
  if (!date) {
    Swal.fire("", "Vui lòng chọn ngày cần xóa", "warning");
    return;
  }

  const { value: password } = await Swal.fire({
    title: `XÓA LOG NGÀY ${toVNDate(date)}`,
    html: `
      <p style="color:red;font-weight:bold;">Thao tác này sẽ xóa toàn bộ giao dịch<br>ngày ${toVNDate(date)}, kể cả đã xuất hóa đơn.</p>
      <p style="color:red;">Bao gồm cả thông tin hóa đơn KH và dữ liệu custom_data.</p>
      <input type="password" id="confirmPassword" class="swal2-input" placeholder="Nhập mật khẩu xác nhận">
    `,
    confirmButtonText: 'Xóa ngay',
    cancelButtonText: 'Hủy',
    confirmButtonColor: '#d33',
    showCancelButton: true,
    preConfirm: () => {
      const val = document.getElementById("confirmPassword").value;
      if (!val) {
        Swal.showValidationMessage("Vui lòng nhập mật khẩu");
      }
      return val;
    }
  });

  if (!password) return;

  try {
    const res = await fetch("/api/delete_logs_by_date", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        date,
        password,
        deleteInvoice: true    // ✅ Yêu cầu xóa cả hóa đơn KH và custom_data
      })
    });

    const json = await res.json();
    if (json.success) {
      Swal.fire("", json.message, "success");
      loadResetLogs();
    } else {
      Swal.fire("", json.message || "Xóa thất bại", "error");
    }
  } catch (err) {
    console.error("❌ Lỗi khi xóa log:", err);
    Swal.fire("❌", "Lỗi hệ thống", "error");
  }
}


//---------------------------------------------------------------------------------------------------
async function showIPWAN() {
  try {
    const res = await fetch("/api/latest_ipwan");
    const data = await res.json();
    const ip = data.ip || "Chưa có";
	const time = data.time ? formatVNDateTime(data.time) : "-";
    Swal.fire("📡 IP WAN MonBox", `<b>Link:</b> ${ip}:25123<br><b>Thời gian:</b> ${time}`, "info");
  } catch (err) {
    Swal.fire("❌ Lỗi", "Không thể lấy thông tin IP WAN.", "error");
  }
}

//---------------------------------------------------------------------------------------------------
async function compareLogs() {
  const date = document.getElementById("filterStartDate").value;
  if (!date) {
    Swal.fire("", "Vui lòng chọn ngày cần so sánh", "warning");
    return;
  }

  try {
    // Bước 1: Lấy IP WAN mới nhất từ MonBox
    const resIP = await fetch("/api/latest_ipwan");
    let { ip } = await resIP.json();
    if (!ip) throw new Error("Không tìm thấy IP WAN MonBox");

    // 👉 Thêm port 25123 nếu IP không có sẵn
    if (!ip.includes(":")) ip = `${ip}:25123`;

    // 👉 Hiển thị link tải CSV để debug
    const [year, month, day] = date.split("-");
    const filename = `${year}${month}${day}.csv`;
    const monboxUrl = `http://${ip}/edit_sdfs?download=/${year}/${month}/${filename}`;

    await Swal.fire({
      title: "Link tải CSV từ MonBox",
      html: `<code style="font-size:14px;">${monboxUrl}</code>`,
      icon: "info"
    });

    // Bước 2: Gọi API backend để so sánh log
    const res = await fetch(`/api/compare_log?date=${date}&ip=${ip}`);
    const json = await res.json();
    if (!json.success) throw new Error(json.message || "Lỗi khi so sánh");

    const { total_csv, total_db, missing_in_db, missing_in_csv } = json;

    // Bước 3: Hiển thị kết quả so sánh
    let html = `
      <b>Tổng log trong DB:</b> ${total_db}<br/>
      <b>Tổng log trong CSV:</b> ${total_csv}<br/><br/>
      <b style="color:red;">🟥 Có ${missing_in_db.length} log CSV chưa có trong DB:</b><br/>
      ${missing_in_db.slice(0, 30).join("<br/>")}${missing_in_db.length > 30 ? "<br/>..." : ""}<br/><br/>
      <b style="color:orange;">🟧 Có ${missing_in_csv.length} log DB không thấy trong CSV:</b><br/>
      ${missing_in_csv.slice(0, 30).join("<br/>")}${missing_in_csv.length > 30 ? "<br/>..." : ""}
    `;

    Swal.fire({
      icon: (missing_in_db.length === 0 && missing_in_csv.length === 0) ? "success" : "warning",
      title: "📊 Kết quả so sánh log",
      html,
      width: 700
    });

  } catch (err) {
    console.error("❌ compareLogs() error:", err);
    Swal.fire("❌ Lỗi", err.message || "Không thể so sánh log", "error");
  }
}

//---------------------------------------------------------------------------------------------------
async function importFromCompareLink() {
  const date = document.getElementById("filterStartDate").value;
  if (!date) {
    Swal.fire("", "Vui lòng chọn ngày", "warning");
    return;
  }

  try {
    const resIP = await fetch("/api/latest_ipwan");
    let { ip } = await resIP.json();
    if (!ip) throw new Error("Không tìm thấy IP WAN MonBox");
    if (!ip.includes(":")) ip += ":25123";

    const [year, month, day] = date.split("-");
    const filename = `${year}${month}${day}.csv`;
    const monboxUrl = `http://${ip}/edit_sdfs?download=/${year}/${month}/${filename}`;

    await Swal.fire({
      title: "Link tải CSV từ MonBox",
      html: `<code style="font-size:14px;">${monboxUrl}</code>`,
      icon: "info"
    });

    const res = await fetch("/api/import_csv_from_ip_wan", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ url: monboxUrl })
    });

    const json = await res.json();
    if (!json.success) throw new Error(json.message);

    Swal.fire("Thành công", `Đã import: ${json.inserted}, Bỏ qua: ${json.skipped}`, "success");
    loadResetLogs();

  } catch (err) {
    console.error("❌ importFromCompareLink() error:", err);
    Swal.fire("❌ Lỗi", err.message || "Import thất bại", "error");
  }
}

// === Xử lý đếm số lượng người truy cập ======================
async function showActiveUsers() {
  try {
    const res = await fetch('/active_users');
    const data = await res.json();
    document.getElementById('activeUserCount').innerText = `🟢 Online: ${data.active}`;
  } catch (e) {
    console.warn("Không thể lấy số người dùng hoạt động.");
  }
}



//---------------------------------------------------------------------------------------------------
window.onload = async function () {
  setDefaultDates(); // Gán ngày mặc định (hôm nay)
  showActiveUsers();
  await loadFuelMap();	
  await loadTankMap();

  // ✅ Đợi input ngày có giá trị rồi mới load logs
  setTimeout(() => {
    loadResetLogs();
  }, 50);

  // ✅ Gắn onchange cho bộ lọc ngày → tải lại logs + kiểm tra thiếu
  document.getElementById("filterStartDate").onchange = async () => {
    if (await validateDateRange_Check()) {
      loadResetLogs();        // Gọi lại BE
      await checkMissingLogs(); // Kiểm tra thiếu log
    }
  };
  document.getElementById("filterEndDate").onchange = async () => {
    if (await validateDateRange_Check()) {
      loadResetLogs();
      await checkMissingLogs();
    }
  };

  // ✅ Các bộ lọc khác chỉ áp dụng trên currentLogs đã tải
  document.getElementById("filterStartTime").onchange = applyResetFilters;
  document.getElementById("filterEndTime").onchange = applyResetFilters;
  document.getElementById("filterFaucet").onchange = applyResetFilters;
  document.getElementById("filterFuel").onchange = applyResetFilters;
  document.getElementById("filterPrice").onchange = applyResetFilters;
  document.getElementById("filterCode").oninput = applyResetFilters;
};

</script>
<script src="{{ url_for('static', filename='main.js') }}"></script>
</body>
</html>
