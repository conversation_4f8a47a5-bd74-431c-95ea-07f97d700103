<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title><PERSON><PERSON><PERSON><PERSON> l<PERSON> h<PERSON> đ<PERSON>n</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet"/>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"/>
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> 
  <style>
	.summary-bar {
	margin-top: 10px;
	margin-bottom: 15px;
	font-weight: bold;
	font-size: 14px;
	background-color: #f8f9fa;
	padding: 8px 12px;
	border: 1px solid #dee2e6;
	border-radius: 6px;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 10px;
	}
	.modal {
	position: fixed;
	top: 0; left: 0;
	width: 100vw; height: 100vh;
	background-color: rgba(0,0,0,0.5);
	align-items: center;
	justify-content: center;
	display: none; /* mặc định ẩn */
	}

	.modal.show {
	display: flex !important; /* khi có class .show thì hiển thị */
	}

	.close {
	float: right;
	font-size: 24px;
	cursor: pointer;
	}
	.modal-content {
	background-color: white;
	padding: 20px;
	border-radius: 10px;
	width: 800px;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 5px 15px rgba(0,0,0,0.3);
	}
	th {
	  background-color: #003366 !important;  /* giữ nền xanh đậm */
	  color: white;
	  text-align: center;
	  vertical-align: middle !important;
	  white-space: normal !important; /* cho phép xuống dòng */
	  padding: 8px 6px;
	  font-size: 13px;
	}

		
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });
</script>

<script>
  // Nếu là thiết bị di động thì chuyển hướng
  if (/Android|iPhone|iPad|iPod|Windows Phone/i.test(navigator.userAgent)) {
    window.location.href = "/check_invoice_mobile";  // ← Đổi đường dẫn tùy theo route bạn đã tạo
  }
</script>

</head>

<body class="d-flex flex-column min-vh-100">
  <div class="container-fluid px-3 flex-grow-1">
	<div class="header-row d-flex align-items-center justify-content-between flex-wrap">
	  <!-- Logo và tên công ty -->
	  <div class="d-flex align-items-center gap-2">
		<img src="/static/favicon.ico" alt="Logo" style="height: 40px; border-radius: 8px; cursor: pointer;" onclick="location.reload()" />
		<div>
		  <h2 id="sellerName" class="m-0" style="color:  #dc3545; font-weight: bold; font-size: 20px;">CÔNG TY MONTECH</h2>
			<p class="m-0" style="font-size: 12px; font-weight: bold; color: #063970; white-space: nowrap;">
			  <span id="sellerTaxcode">**********</span>
			  <span id="sellerFullName">Montech</span>
			  <span id="providerLabel" class="text-muted ms-1" style="font-weight: normal;"></span>
			</p>
		</div>
	  </div>

	  <!-- Phần bên phải: Cập nhật + Cài đặt + User -->
	  <div class="d-flex align-items-center gap-3">
		<!-- Trạng thái cập nhật -->
		<div id="fetchStatus" style="font-size: 14px; color: gray; text-align: center;"></div>
		<div id="activeUserCount" style="font-size: 14px; color: gray; text-align: center;"></div>		
	  </div>
	  
	</div>
    <div style="height: 1px; background-color: #ccc; margin: 6px 0;"></div>
	
    <!-- Bộ lọc -->
    <div class="row g-1 align-items-end mb-1 text-center">
	  <div class="col">
		<label class="form-label w-100">Từ Ngày</label>
		<input type="date" id="filterStartDate" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Đến Ngày</label>
		<input type="date" id="filterEndDate" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Từ Giờ</label>
		<input type="time" id="filterStartTime" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Đến Giờ</label>
		<input type="time" id="filterEndTime" class="form-control">
	  </div>
	  <div class="col">
		<label class="form-label w-100">Cò</label>
		<select id="filterFaucet" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Nhiên liệu</label>
		<select id="filterFuel" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Đơn giá</label>
		<select id="filterPrice" class="form-select"></select>
	  </div>
	  <div class="col">
		<label class="form-label w-100">Bồn</label>
		<select id="filterTank" class="form-select"></select>
	  </div>
	  <div class="col">	  	  
		<label class="form-label w-100">Tìm kiếm</label>
		<input type="text" id="search" class="form-control">
	  </div>
	  
	  <div class="col-auto" style="display: none;">
		  <div class="form-check">
			<input class="form-check-input" type="checkbox" id="filterIsInvoiced" checked>
			<label class="form-check-label" for="filterIsInvoiced">
			  GD đã có hóa đơn
			</label>
		  </div>
	  </div>
	  
	  <div class="col-auto">
		<button class="btn btn-success btn-sm" onclick="exportExcelByDate()">Xuất File</button>
	  </div>
	
	</div>
	
	<!------------- Bảng log ----------------------------------------------------------------------->
	<div class="d-flex flex-wrap gap-2 mb-2" style="align-items: start;">
	  <div id="logSummary" class="flex-fill"></div>
	  <div id="invoiceStats" class="flex-fill"></div>
	</div>

    <div class="table-wrapper">
      <table id="logTable" class="table table-bordered table-hover">
		<thead>
		  <tr>
			<th class="sortable" onclick="sortTable(this)">NGÀY <i class="fa fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(this)">GIỜ <i class="fa fa-sort"></i></th>
			<th>CÒ</th>
			<th>NHIÊN LIỆU</th>
			<th class="sortable" onclick="sortTable(this)">SỐ TIỀN <i class="fa fa-sort"></i></th>
			<th class="sortable" onclick="sortTable(this)">SỐ LÍT <i class="fa fa-sort"></i></th>
			<th>ĐƠN GIÁ</th>
			<th class="sortable" onclick="sortTable(this)">SỐ HĐ <i class="fa fa-sort"></i></th>
			<th>CK</th>
			<th>MÃ KH</th>
			<th>NGƯỜI MUA</th>			
			<th>CÔNG TY</th>			
			<th>MST</th>
			<th>EMAIL</th>
			<th>CCCD</th>
			<th>MÃ ĐVQHNS</th>				
			<th>SERIAL</th>
			<th class="sortable" onclick="sortTable(this)">BỒN <i class="fa fa-sort"></i></th>		
			<th>MÃ GD</th>
		  </tr>
		</thead>
        <tbody id="logResetBody"></tbody>
      </table>
    </div>
	<!--------------------------------------------------------------------------------->
  
  </div>


<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>

<script>
//---------------------------------------------------------------------------------------------------
function toVNDate(dateStr) {
	if (!dateStr || dateStr.indexOf("-") === -1) return dateStr;
	const dArr = dateStr.split("-");
	return `${dArr[2]}-${dArr[1]}-${dArr[0]}`;
}

//---------------------------------------------------------------------------------------------------
function formatVNDateTime(dateTimeStr) {
  if (!dateTimeStr.includes("T") && !dateTimeStr.includes(" ")) return dateTimeStr;
  const dt = new Date(dateTimeStr);
  if (isNaN(dt)) return dateTimeStr;

  const pad = n => n.toString().padStart(2, "0");
  const dd = pad(dt.getDate());
  const mm = pad(dt.getMonth() + 1);
  const yyyy = dt.getFullYear();
  const hh = pad(dt.getHours());
  const mi = pad(dt.getMinutes());
  const ss = pad(dt.getSeconds());

  return `${dd}-${mm}-${yyyy} ${hh}:${mi}:${ss}`;
}

//---------------------------------------------------------------------------------------------------
let fuelMap = {};
async function loadFuelMap() {
  try {
    const res = await fetch("/fuel_map");
    fuelMap = await res.json();
  } catch (e) {
    console.error("❌ Lỗi khi load fuelMap:", e);
    fuelMap = {};
  }
}

//---------------------------------------------------------------------------------------------------
function getMappedFuelName(code) {
  return fuelMap?.[code] || code || "";
}


//---------------------------------------------------------------------------------------------------
let tankMap = {};
async function loadTankMap() {
  try {
    const res = await fetch("/tank_map");
    tankMap = await res.json();
  } catch (e) {
    console.error("❌ Lỗi khi load tankMap:", e);
    tankMap = {};
  }
}

//---------------------------------------------------------------------------------------------------
function getMappedTankName(serial) {
  return tankMap?.[serial] || "";
}


//---------------------------------------------------------------------------------------------------
let currentLogs = [];
function loadResetLogs() {
  updateFetchStatus("🟢 Đang tải GD...");

  const start = document.getElementById("filterStartDate")?.value;
  const end = document.getElementById("filterEndDate")?.value;

  fetch(`/logs?start=${start}&end=${end}`)
    .then(res => res.json())
    .then(data => {
      currentLogs = data.logs;
      applyResetFilters();
      updateSearchSuggestions();

      const now = new Date().toLocaleTimeString('vi-VN', { hour12: false });
      updateFetchStatus(`✅ Cập nhật ${now}`);
    })
    .catch(err => {
      console.error("❌ Lỗi tải logs:", err);
      updateFetchStatus("❌ Không thể tải logs");
    });
}

//---------------------------------------------------------------------------------------------------
let tomSelectInstance;

function updateSearchSuggestions() {
  const input = document.getElementById("search");
  if (!input) return;

  const list = new Set();

  for (const log of currentLogs) {
    if (log.transactionCode) list.add(log.transactionCode);
	if (log.invoiceNumber) list.add(log.invoiceNumber);
    if (log.custom_data?.mkhang) list.add(log.custom_data.mkhang);
    if (log.custom_data?.ho_ten) list.add(log.custom_data.ho_ten);
    if (log.custom_data?.ten) list.add(log.custom_data.ten);
    if (log.custom_data?.mst) list.add(log.custom_data.mst);
    if (log.custom_data?.email) list.add(log.custom_data.email);
    if (log.custom_data?.plate) list.add(log.custom_data.plate);
    if (log.custom_data?.phone) list.add(log.custom_data.phone);
    if (log.custom_data?.mdvqhns) list.add(log.custom_data.mdvqhns);	
	
	  // Thêm số tiền
    if (log.transactionCost) {
	  const raw = log.transactionCost.toString();              // "500000"
	  const formatted = log.transactionCost.toLocaleString('vi-VN'); // "500.000"
	  list.add(raw);
	  list.add(formatted);
	}

    // Thêm số lít (đổi về lít)
	if (log.transactionAmount) {
	  const litres = (log.transactionAmount / 1000).toFixed(3); // "5.000"
	  const rawLitres = litres.replace('.', '');               // "5000"
	  list.add(litres);
	  list.add(rawLitres);
	}
 
  }

  const options = Array.from(list).map(val => ({
    value: val,
    text: val
  }));

  if (tomSelectInstance) tomSelectInstance.destroy();

  tomSelectInstance = new TomSelect("#search", {
    options,
    maxItems: 1,
    create: false,
    persist: false,
    hideSelected: true,
    allowEmptyOption: true,
    openOnFocus: false,
    placeholder: "Tiền, số HĐ, tên Cty, MST...",
    render: {
      item: function (data, escape) {
        // Không hiển thị tag đã chọn để tránh lệch giao diện
        return `<div style="display: none;">${escape(data.text)}</div>`;
      }
    },
    onChange: (value) => {
      if (value && value.trim()) {
        applyResetFilters(value); // truyền keyword vào lọc
      }
    }
  });
}


//---------------------------------------------------------------------------------------------------
function fillSelect(id, values) {
  const select = document.getElementById(id);
  const current = select.value;
  select.innerHTML = '<option value="">Tất cả</option>';

  [...values]
    .sort((a, b) => Number(a) - Number(b))  // đảm bảo đúng thứ tự số
    .forEach(val => {
      const label =
        id === "filterFuel" ? (window.fuelMap?.[val] || val) :
        id === "filterTank" ? val :  // hoặc custom thêm nếu muốn gắn tên bồn
        val;

      select.innerHTML += `<option value="${val}">${label}</option>`;
    });

  select.value = current;
}


//---------------------------------------------------------------------------------------------------
function updateResetDropdowns(logs) {
  const selectedFaucet = document.getElementById("filterFaucet").value;
  const selectedFuel = document.getElementById("filterFuel").value;
  const selectedPrice = document.getElementById("filterPrice").value;
  const selectedTank = document.getElementById("filterTank").value;  

  const faucets = new Set();
  const fuels = new Set();
  const prices = new Set();
  const tanks = new Set();
  
  for (let log of logs) {
    const matchFaucet = !selectedFaucet || log.faucetNo == selectedFaucet;
    const matchFuel = !selectedFuel || log.fuelType === selectedFuel;
    const matchPrice = !selectedPrice || log.transactionPrice == selectedPrice;

    if (matchFuel && matchPrice) faucets.add(log.faucetNo);
    if (matchFaucet && matchPrice) fuels.add(log.fuelType);
    if (matchFuel && matchFaucet) prices.add(log.transactionPrice);
	if (matchFuel && matchFaucet && matchPrice) {
	  const tankName = getMappedTankName(log.pumpSerial);
	  if (tankName) tanks.add(tankName);
	}
  }

  fillSelect("filterFaucet", faucets);
  fillSelect("filterFuel", fuels);
  fillSelect("filterPrice", prices);
  fillSelect("filterTank", tanks);  
}
//---------------------------------------------------------------------------------------------------
function applyResetFilters() {
	updateResetDropdowns(currentLogs); 

	const onlyInvoiced = document.getElementById("filterIsInvoiced").checked;
	const startDate = document.getElementById("filterStartDate").value;
	const endDate = document.getElementById("filterEndDate").value;
	const startTime = document.getElementById("filterStartTime").value;
	const endTime = document.getElementById("filterEndTime").value;
	const faucet = document.getElementById("filterFaucet").value;
	const fuel = document.getElementById("filterFuel").value;
	const price = document.getElementById("filterPrice").value;
	const tank = document.getElementById("filterTank").value;	
	const keyword = document.getElementById("search")?.value.trim().toLowerCase();

	const normalizedKeyword = keyword?.replace(/\./g, '').replace(',', '.');

	const filtered = currentLogs.filter(log => {
		if (onlyInvoiced && !log.isInvoiced) return false;
		if (onlyInvoiced) {
			const inv = log.invoiceNumber;
			if (!inv || inv === "Nội bộ" || inv === "CHUA_RO_SOHĐ" || inv === "0") return false;
			}
		if (startDate && log.transactionDate < startDate) return false;
		if (endDate && log.transactionDate > endDate) return false;
		if (startTime && log.transactionTime < startTime) return false;
		if (endTime && log.transactionTime > endTime) return false;
		if (faucet && String(log.faucetNo) !== faucet) return false;
		if (fuel && log.fuelType !== fuel) return false;
		if (price && String(log.transactionPrice) !== price) return false;
		if (tank && getMappedTankName(log.pumpSerial) !== tank) return false;
		
		if (normalizedKeyword) {
			const rawCost = log.transactionCost || 0;
			const rawAmount = (log.transactionAmount || 0) / 1000;
			const roundedAmountStr = rawAmount.toFixed(3).replace(',', '.');  // "10.000"

			// So khớp số tiền tuyệt đối
			if (normalizedKeyword === rawCost.toString()) return true;

			// So khớp số lít tuyệt đối
			if (
				normalizedKeyword === rawAmount.toFixed(0) ||     // Gõ 10
				normalizedKeyword === roundedAmountStr.replace('.', '') || // Gõ 10000 so với 10.000
				normalizedKeyword === roundedAmountStr            // Gõ 10.000
			) return true;

			// So khớp các trường text khác
			const fields = [
				log.transactionCode,
				log.invoiceNumber,
				log.custom_data?.mkhang,
				log.custom_data?.ho_ten,
				log.custom_data?.ten,
				log.custom_data?.mst,
				log.custom_data?.email,
				log.custom_data?.plate,
				log.custom_data?.phone,
				log.custom_data?.mdvqhns
			].map(x => (x || "").toLowerCase());

			const haystack = fields.join(" ");
			if (!haystack.includes(normalizedKeyword)) return false;
		}

		return true;
	});

	window.currentFilteredLogs = filtered;
	renderFilteredResetLogs(filtered);
}

//---------------------------------------------------------------------------------------------------
function renderFilteredResetLogs(logs) {
  window.currentFilteredLogs = logs;  //Phục vụ xuất Excel đúng dữ liệu đã lọc
  window.currentLogs = logs;  //Phục vụ generateQR
  logs.sort((a, b) => {
    const n1 = parseInt(a.invoiceNumber) || 0;
    const n2 = parseInt(b.invoiceNumber) || 0;
    return n2 - n1;
  });
  const keyword = document.getElementById("search")?.value.trim().toLowerCase() || "";
  const normalizedKeyword = keyword.replace(/\./g, '').replace(',', '.');

  const totalInternal = logs.filter(l => l.invoiceNumber === "Nội bộ").length;
  const totalNonInternal = logs.length - totalInternal;
  
	const totalNoInvoiceNumberAll = currentLogs.filter(l =>
	  !l.invoiceNumber || l.invoiceNumber === "CHUA_RO_SOHĐ" || l.invoiceNumber === "0"
	).length;



  const tbody = document.getElementById("logResetBody");
  const summary = document.getElementById("logSummary");
  const statsDiv = document.getElementById("invoiceStats");
  tbody.innerHTML = '';

  let sumCost = 0, sumLitre = 0;
  logs.forEach(log => {
    sumCost += log.transactionCost;
    sumLitre += log.transactionAmount / 1000;
  });

  const invoiceObjects = logs
    .map(l => l.invoiceNumber)
    .filter(x => typeof x === "string" && x.trim() !== "" && x !== "CHUA_RO_SOHĐ" && x !== "Nội bộ")
    .map(x => ({
      original: x,
      sortKey: parseInt(x.split('_')[0]) || 0
    }))
    .sort((a, b) => a.sortKey - b.sortKey);

  const invoiceNumbers = invoiceObjects.map(x => x.original);
  const hasUnderscore = invoiceNumbers.some(inv => inv.includes("_"));

  const faucet = document.getElementById("filterFaucet").value;
  const fuel = document.getElementById("filterFuel").value;
  const price = document.getElementById("filterPrice").value;
  const tank = document.getElementById("filterTank").value;  
  const isStrictFilter = faucet || fuel || price || tank || normalizedKeyword;


  let minInvoice = null, maxInvoice = null, missingCount = 0, missingList = [];

  if (isStrictFilter) {
    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${localStorage.getItem("filterIsInvoiced") === "1" ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary" style="font-weight: bold;">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label text-danger">Đang lọc, bỏ qua kiểm tra thiếu số HĐ</span>
      </div>
    `;
  } else if (!hasUnderscore && invoiceObjects.length > 0) {
    minInvoice = invoiceObjects[0].sortKey;
    maxInvoice = invoiceObjects[invoiceObjects.length - 1].sortKey;
    const fullSet = new Set(invoiceObjects.map(x => x.sortKey));
    const maxGap = maxInvoice - minInvoice;

    if (maxGap < 5000) {
      for (let i = minInvoice; i <= maxInvoice; i++) {
        if (!fullSet.has(i)) {
          missingCount++;
          missingList.push(i);
        }
      }
    }

    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Số HĐ nhỏ nhất:</span> <span class="stat-number text-success">${minInvoice}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Số HĐ lớn nhất:</span> <span class="stat-number text-success">${maxInvoice}</span>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Thiếu:</span> <span class="stat-number text-danger">${missingCount}</span>
        ${missingList.length > 0 ? `<br/><span class="label">Danh sách thiếu:</span> <span class="stat-number text-danger">${missingList.join(", ")}</span>` : ""}
      </div>
    `;
  } else {
    statsDiv.innerHTML = `
      <div class="border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
        <input class="form-check-input" type="checkbox" id="filterIsInvoicedSummary" ${document.getElementById("filterIsInvoiced")?.checked ? 'checked' : ''}>
        <label class="form-check-label" for="filterIsInvoicedSummary">Đã xuất HĐ</label>
        &nbsp;&nbsp;&nbsp;
        <span class="label">Tổng HĐ:</span> <span class="stat-number text-success">${invoiceNumbers.length}</span>
        &nbsp;&nbsp;&nbsp;
        <!--<span class="label text-danger">Đang lọc bỏ qua kiểm tra HĐ</span> -->
      </div>
    `;
  }

	const totalUninvoiced = logs.filter(l => !l.invoiceNumber || l.invoiceNumber === "CHUA_RO_SOHĐ" || l.invoiceNumber === "0").length;

	summary.innerHTML = `
	  <div class="d-flex flex-wrap justify-content-between align-items-center border p-2 bg-light rounded" style="font-size: 13px; font-weight: bold;">
		<div>
		  <span class="label">Tổng tiền:</span> <span class="stat-number text-primary">${sumCost.toLocaleString('vi-VN')}</span> đồng
		  &nbsp;&nbsp;&nbsp;
		  <span class="label">Tổng lít:</span> <span class="stat-number text-primary">${sumLitre.toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</span> lít
		  &nbsp;&nbsp;&nbsp;
		  <span class="label">Tổng GD:</span> <span class="stat-number text-primary">${logs.length}</span>
		  &nbsp;&nbsp;&nbsp;
		  <span class="label">Chưa xuất:</span> <span class="stat-number text-danger">${totalNonInternal - invoiceNumbers.length}</span>
		  &nbsp;&nbsp;&nbsp;
		  <span class="label">Nội bộ:</span> <span class="stat-number text-secondary">${totalInternal}</span>
		  &nbsp;&nbsp;&nbsp;
		  <span class="label">Đã CK:</span> <span class="stat-number text-success">${logs.filter(log => log.qr_paid).length}</span>
		  <br/>
		  <span class="label text-primary">Số lượng GD chưa xuất hóa đơn:</span> <span class="stat-number text-danger">${totalNoInvoiceNumberAll}</span>
		</div>
	  </div>
	`;



	document.getElementById("filterIsInvoicedSummary").addEventListener("change", (e) => {
	  const checked = e.target.checked;
	  document.getElementById("filterIsInvoiced").checked = checked;
	  localStorage.setItem("filterIsInvoiced", checked ? "1" : "0");
	  applyResetFilters();
	});


	for (let log of logs) {
	  const tr = document.createElement("tr");

	  tr.innerHTML = `
		<td>${toVNDate(log.transactionDate)}</td>
		<td>${log.transactionTime}</td>
		<td>${log.faucetNo}</td>
		<td>${getMappedFuelName(log.fuelType)}</td>		
		<td>${log.transactionCost.toLocaleString('vi-VN')}</td>
		<td>${(log.transactionAmount / 1000).toLocaleString('vi-VN', { minimumFractionDigits: 3 })}</td>
		<td>${log.transactionPrice.toLocaleString('vi-VN')}</td>

		<!-- Cột số hóa đơn -->
		<td class="text-center">
		  ${
			log.invoiceNumber === "Nội bộ"
			  ? `<span class="badge bg-secondary text-light rounded-pill px-3 py-1">Nội bộ</span>`
			: log.invoiceNumber && log.invoiceNumber !== "CHUA_RO_SOHĐ" && log.invoiceNumber !== "0"
			  ? `<span class="badge bg-success rounded-pill px-2 py-1" style="font-size: 12px;">${log.invoiceNumber}</span>`
			  : `<i class="fa-regular fa-file text-danger" style="font-size: 15px;"></i>`
		  }
		</td>

		<!-- Cột QR -->
		<td>
		  <i class="fa-solid fa-qrcode ${log.qr_paid ? 'text-success' : 'text-danger'}"
			 style="font-size: 18px; cursor: pointer;"
			 onclick='generateQR("${log.transactionCode}")'></i>
		</td>

		<!-- 4 cột custom_data -->
		<td>${log.custom_data?.mkhang || ''}</td>
		<td>${log.custom_data?.ho_ten || ''}</td>
		<td>${log.custom_data?.ten || ''}</td>
		<td>${log.custom_data?.mst || ''}</td>
		<td>${log.custom_data?.email || ''}</td>
		<td>${log.custom_data?.cccd || ''}</td>		
		<td>${log.custom_data?.mdvqhns || ''}</td>			
		<!-- Các cột bổ sung -->
		<td>${log.pumpSerial}</td>
		<td>${getMappedTankName(log.pumpSerial)}</td>
		<td>${log.transactionCode}</td>
	  `;

	  tbody.appendChild(tr);
	}
}

//---------------------------------------------------------------------------------------------------
function toggleSelectAll(master) {
	const checkboxes = document.querySelectorAll(".row-checkbox");
	checkboxes.forEach(cb => cb.checked = master.checked);
}

//---------------------------------------------------------------------------------------------------
function sortTable(thElement) {
	const table = thElement.closest("table");
	const tbody = table.querySelector("tbody");
	const rows = Array.from(tbody.querySelectorAll("tr"));

	// Tính colIndex động theo vị trí thực tế của th
	const headers = Array.from(thElement.parentElement.children);
	const colIndex = headers.indexOf(thElement);

	const asc = table.dataset.sortCol == colIndex && table.dataset.sortDir !== "asc";
	table.dataset.sortCol = colIndex;
	table.dataset.sortDir = asc ? "asc" : "desc";

	const parseMoney = (str) => parseFloat(str.replace(/\./g, '').replace(/,/g, '.')) || 0;
	const parseLit = (str) => parseFloat(str.replace(",", ".")) || 0;
	const parseTime = (str) => {
		const parts = str.split(":").map(Number);
		return parts.length === 3 ? parts[0] * 3600 + parts[1] * 60 + parts[2] : 0;
	};

	rows.sort((a, b) => {
		let v1 = a.cells[colIndex]?.textContent.trim() || '';
		let v2 = b.cells[colIndex]?.textContent.trim() || '';

		let n1 = v1, n2 = v2;

		if (v1.includes("₫") || v1.match(/^\d{1,3}(\.\d{3})*(,\d+)?$/)) {
			n1 = parseMoney(v1);
			n2 = parseMoney(v2);
		} else if (v1.match(/^\d+,\d+$/) || v1.match(/^\d{1,3}(,\d{3})*$/)) {
			n1 = parseLit(v1);
			n2 = parseLit(v2);
		} else if (v1.match(/^\d{1,2}:\d{2}:\d{2}$/)) {
			n1 = parseTime(v1);
			n2 = parseTime(v2);
		} else {
			n1 = v1.toLowerCase();
			n2 = v2.toLowerCase();
		}

		if (typeof n1 === "string") {
			return asc ? n1.localeCompare(n2) : n2.localeCompare(n1);
		} else {
			return asc ? n1 - n2 : n2 - n1;
		}
	});

	tbody.innerHTML = "";
	rows.forEach(row => tbody.appendChild(row));
}

//---------------------------------------------------------------------------------------------------
function getTodayVN() {
	const now = new Date();
	now.setHours(now.getHours() + 7);
	return now.toISOString().slice(0, 10);
}
//---------------------------------------------------------------------------------------------------
function setDefaultDates() {
  const todayStr = getTodayVN();
  document.getElementById("filterStartDate").value = todayStr;
  document.getElementById("filterEndDate").value = todayStr;

  // Bỏ tích mặc định ô "Hiển thị GD đã có hóa đơn"
  document.getElementById("filterIsInvoiced").checked = false;
}

//---------------------------------------------------------------------------------------------------
async function validateDateRange_Check(showPopup = true) {
  const startInput = document.getElementById('filterStartDate');
  const endInput = document.getElementById('filterEndDate');

  const start = new Date(startInput.value);
  const end = new Date(endInput.value);

  if (!start || !end || isNaN(start) || isNaN(end)) return false;

  const diffMs = end.getTime() - start.getTime();
  const diffDays = diffMs / (1000 * 60 * 60 * 24);

  if (diffDays > 92) {
    await Swal.fire({
      icon: 'warning',
      title: 'Khoảng thời gian quá dài!',
      html: `Bạn chỉ được chọn tối đa <b>90 ngày</b>.<br>Hiện tại đang chọn <b>${diffDays.toFixed(0)}</b> ngày.`,
      confirmButtonText: 'OK'
    });
    return false;
  }

  if (diffDays > 0 && showPopup) {
    await Swal.fire({
      icon: 'info',
      html: `Bạn đang chọn <b>${diffDays.toFixed(0)}</b> ngày.`,
      confirmButtonText: 'OK'
    });
  }

  return true;
}

//---------------------------------------------------------------------------------------------------
window.onload = async function () {
  setDefaultDates(); // Gán ngày mặc định

  showActiveUsers();

  const saved = localStorage.getItem("filterIsInvoiced");
  if (saved !== null) {
    const checked = saved === "1";
    document.getElementById("filterIsInvoiced").checked = checked;
  }

  await loadFuelMap();	
  await loadTankMap();

  // Đợi DOM nhận giá trị input date xong rồi mới gọi
  setTimeout(() => {
    loadResetLogs();  // Tải logs theo ngày mặc định
  }, 50);

  // Gắn onchange ngày → gọi lại BE nếu hợp lệ
  document.getElementById("filterStartDate").onchange = async () => {
    if (await validateDateRange_Check()) {
      loadResetLogs(); // Tải lại từ BE
    }
  };
  document.getElementById("filterEndDate").onchange = async () => {
    if (await validateDateRange_Check()) {
      loadResetLogs();
    }
  };

  // Các bộ lọc khác chỉ cần lọc lại trên currentLogs
  document.getElementById("filterStartTime").onchange = applyResetFilters;
  document.getElementById("filterEndTime").onchange = applyResetFilters;
  document.getElementById("filterFaucet").onchange = applyResetFilters;
  document.getElementById("filterFuel").onchange = applyResetFilters;
  document.getElementById("filterPrice").onchange = applyResetFilters;
  document.getElementById("filterTank").onchange = applyResetFilters;
  document.getElementById("search").addEventListener("change", applyResetFilters);
};

//--------------------------------------------------------------------------------------------
let currentFilteredLogs = [];
let userIsInteracting = false;
let interactionTimeout = null;
let currentPage = 1;
const perPage = 100;
let allLogs = [];              // Chứa toàn bộ log
let lastChecked = null; // Biến bấm shift checkbox
let logsPerPage = parseInt(document.getElementById("logsPerPageSelect")?.value) || 100;

fetch("/load_config_invoice")
  .then(res => res.json())
  .then(config => {
    window.currentConfig = config;

    const provider = config.provider?.toLowerCase() || "vnpt";

    // Cập nhật dòng providerLabel
    const labelEl = document.getElementById("providerLabel");
    if (labelEl) labelEl.textContent = `(${provider.toUpperCase()})`;

    // Cập nhật trạng thái auto gửi
    //updateAutoConfigStatus();

    // Cập nhật tên công ty + MST
    const conf = config[provider.toUpperCase()] || {};
    const nameEl = document.getElementById("sellerName");
    const taxEl = document.getElementById("sellerTaxcode");
    const FullNameEl = document.getElementById("sellerFullName");	

    if (nameEl) nameEl.textContent = conf.sellerName || "";
    if (taxEl) taxEl.textContent = `MST: ${conf.sellerTaxCode || ""} - `;
	if (FullNameEl) FullNameEl.textContent = conf.sellerFullName || "";
  })
  .catch(err => {
    console.error("❌ Lỗi load config:", err);
  });


// ======== Xử lý đếm số lượng người truy cập ==================================
async function showActiveUsers() {
  try {
    const res = await fetch('/active_users');
    const data = await res.json();
    document.getElementById('activeUserCount').innerText = `🟢 Online: ${data.active}`;
  } catch (e) {
    console.warn("Không thể lấy số người dùng hoạt động.");
  }
}

setInterval(showActiveUsers, 15000); // cập nhật mỗi 15 giây

// ========= BẤM NÚT XUẤT GD RA EXCEL CÓ THEO BỘ LỌC ===========================
async function exportExcelByDate() {
  const logs = window.currentFilteredLogs || [];
  if (!logs.length) {
    alert("Không có giao dịch nào để xuất.");
    return;
  }

  const codes = logs.map(log => log.transactionCode).filter(Boolean);
  console.log("🧪 Xuất Excel với mã:", codes);

  const res = await fetch('/export_logs_excel', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ codes })
  });

  if (!res.ok) {
    const result = await res.json().catch(() => ({}));
    alert(result.message || "Xuất file thất bại.");
    return;
  }

  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  
  const now = new Date();
  const pad = n => n.toString().padStart(2, '0');
  const dd = pad(now.getDate());
  const mm = pad(now.getMonth() + 1);
  const yyyy = now.getFullYear();
  const hh = pad(now.getHours());
  const mi = pad(now.getMinutes());
  const ss = pad(now.getSeconds());

  const fileName = `Giao_dich_MontechPOS_${dd}${mm}${yyyy}_${hh}${mi}${ss}.xlsx`;

  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);

  // ✅ Tránh lỗi blob trên các trình duyệt, đảm bảo ổn định
  setTimeout(() => {
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);  // ✅ Giải phóng blob
  }, 0);
}


//--------------------------------------------------------------------------------------------
function renderPreviewForm(data) {
  document.getElementById("formHoTen").value = data.ho_ten || "";
  document.getElementById("formTen").value = data.ten || "";
  document.getElementById("formMST").value = data.mst || "";
  document.getElementById("formDChi").value = data.dchi || "";
  document.getElementById("formHTTToan").value = data.httt || "";
  document.getElementById("formTThue").value = data.thueStr || String(data.thue) || "10";
  document.getElementById("formMKHang").value = data.mkhang || "";
  document.getElementById("formTHHDVu").value = data.thhdv || "";
}

//--------------------------------------------------------------------------------------------
// Nút trạng thái đang cập nhật log
function updateFetchStatus(status) {
  const el = document.getElementById("fetchStatus");
  if (!el) return;
  el.innerText = status;
}



</script>
</body>
</html>
