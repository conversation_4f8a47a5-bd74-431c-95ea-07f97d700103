import os
import sys
import time
import json
import threading
import requests
import logging
from logging.handlers import <PERSON><PERSON><PERSON>F<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta

from app import app, db, Log, APP_PORT
from send_invoice import send_invoice_from_log

#-----------------------------------------------------------------------------------------------------------------
CONFIG_PATH = os.path.join("config", "settings.json")
FUEL_MAP = {}
try:
    with open("config/fuel.json", "r", encoding="utf-8") as f:
        FUEL_MAP = json.load(f)
except Exception as e:
    logger.info(f"⚠️ Không thể load fuel.json: {e}")
#-----------------------------------------------------------------------------------------------------------------
os.makedirs("debug/logs_auto_scheduler", exist_ok=True)
logger = logging.getLogger("auto_scheduler")
logger.setLevel(logging.INFO)
#-----------------------------------------------------------------------------------------------------------------
isInvoiceProcessing = False

if not logger.handlers:
    handler = RotatingFileHandler("debug/logs_auto_scheduler/auto_scheduler.log", maxBytes=1_000_000, backupCount=10, encoding="utf-8")
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
#-----------------------------------------------------------------------------------------------------------------
def load_setting():
    with open(CONFIG_PATH, "r", encoding="utf-8") as f:
        return json.load(f)
#-----------------------------------------------------------------------------------------------------------------
def build_default_buyer(setting):
    return {
        "ho_ten": setting.get("defaultCustomerFullName", ""),
        "ten": setting.get("defaultCustomerName", ""),
        "mst": setting.get("defaultCustomerTaxCode", ""),
        "dchi": setting.get("defaultCustomerAddress", ""),
        "httt": setting.get("defaultCustomerHTTToan", "Tiền mặt/Chuyển khoản"),
        "thue": setting.get("defaultCustomerTThue", "10"),
        "mkhang": setting.get("defaultCustomerMKHang", "")
    }
#-----------------------------------------------------------------------------------------------------------------
def show_config_summary(setting, buyer_info):
    logger.info("============================= THÔNG TIN CẤU HÌNH SETTING HIỆN TẠI =============================")
    if setting.get("autoScheduleEnabled"):
        times = ", ".join(setting.get("autoScheduleTimes", []))
        logger.info(f"🔁 CHẾ ĐỘ 1: BẬT | Giờ định sẵn: {times}")
    else:
        logger.info("⏸️ CHẾ ĐỘ 1: TẮT")

    if setting.get("autoEveryXEnabled"):
        logger.info(f"🔁 CHẾ ĐỘ 2: BẬT | Xuất mỗi {setting.get('autoEveryXMin')} phút | Chừa GD trong {setting.get('autoSkipYMin')} phút gần nhất | Với số tiền < {setting.get('autoMaxAmount'):,}đ")
    else:
        logger.info("⏸️ CHẾ ĐỘ 2: TẮT")

    logger.info("👤 NGƯỜI MUA MẶC ĐỊNH:")
    logger.info(f"   - Mã KH: {buyer_info['mkhang']}")
    logger.info(f"   - Tên người mua: {buyer_info['ho_ten']}")
    logger.info(f"   - Tên đơn vị: {buyer_info['ten']}")
    logger.info(f"   - MST: {buyer_info['mst']}")
    logger.info(f"   - Địa chỉ: {buyer_info['dchi']}")
    logger.info(f"   - Thuế suất: {buyer_info['thue']}%")
    logger.info(f"   - Hình thức TT: {buyer_info['httt']}")

    try:
        with open("config/fuel.json", "r", encoding="utf-8") as f:
            fuels = json.load(f)
        logger.info("🛢️ DANH SÁCH NHIÊN LIỆU:")
        for code, name in fuels.items():
            logger.info(f"   - {code:<8} → {name}")
    except Exception as e:
        logger.info(f"⚠️ Không thể đọc fuel.json: {e}")
        
    try:
        with open("config/config.json", "r", encoding="utf-8") as f:
            config_data = json.load(f)
        provider = config_data.get("provider", "vnpt").upper()
        logger.info(f"🧾 NHÀ CUNG CẤP HÓA ĐƠN: {provider}")
    except Exception as e:
        logger.info("🧾 NHÀ CUNG CẤP HÓA ĐƠN: KHÔNG XÁC ĐỊNH")
       
    logger.info("===============================================================================================")

#-----------------------------------------------------------------------------------------------------------------
def import_logs_before_auto():
    try:
        logger.info("📥 Đang gọi import_log từ MonBox trước khi auto xuất hóa đơn...")
        r = requests.post(f"http://127.0.0.1:{APP_PORT}/api/import_log", timeout=10)
        if r.status_code == 200:
            logger.info("✅ Import log thành công.")
        else:
            logger.warning(f"⚠️ Import log lỗi: {r.status_code}")
    except Exception as e:
        logger.warning(f"❌ Lỗi gọi import_log: {e}")

#==================== Mode_1 CHẾ ĐỘ 1 ===================================================================================
#   Chạy tự động vào các khung giờ cố định do bạn cấu hình sẵn (ví dụ: ["22:00:00", "23:45:00", "23:59:00"])
#   Xuất hóa đơn cho tất cả các log trong ngày hôm nay mà chưa xuất hóa đơn
def run_mode_1(setting, buyer_info):
    now = datetime.now().replace(microsecond=0)
    now_str = now.strftime("%H:%M:%S")
    today_str = now.strftime("%Y-%m-%d")

    logger.info("▶ CHẾ ĐỘ 1: Gửi hóa đơn vào các khung giờ cố định")

    with app.app_context():
        db.session.remove()
        db.session.expire_all()
        logger.info("♻️ Đã gọi expire_all() để reload dữ liệu tươi từ DB")

        all_today_logs = db.session.query(Log).filter(Log.transactionDate == today_str).all()
        logger.info(f"🧪 Tổng log ngày {today_str}: {len(all_today_logs)}")

        logs1 = [l for l in all_today_logs if not l.isInvoiced or l.isInvoiced in [0, False, None]]
        logger.info(f"🧪 Sau lọc isInvoiced == False/0: {len(logs1)}")

        logs2 = [l for l in logs1 if not l.invoiceNumber or l.invoiceNumber in ["", "0", "null", None]]
        logger.info(f"🧪 Sau lọc invoiceNumber rỗng: {len(logs2)}")

        logs = sorted(logs2, key=lambda l: (l.transactionDate, l.transactionTime))

        if not logs:
            logger.info(f"⚠️ Không tìm thấy log cần xuất cho ngày {today_str}")
            return

        for i, l in enumerate(logs[:2]):
            logger.info(f"🧾 Mẫu log {i+1}: {l.transactionCode} | HĐ={l.invoiceNumber} | isInv={l.isInvoiced}")

        if not lock_invoice_send():
            return
        
        global isInvoiceProcessing, log_sent_count, log_total_count
        isInvoiceProcessing = True
        log_sent_count = 0
        log_total_count = len(logs)

        logger.info(f"▶ ĐANG CHẠY CHẾ ĐỘ 1... ({len(logs)} log)")

        try:
            for idx, log in enumerate(logs):
                # 🔁 Kiểm tra nếu người dùng đã bấm dừng (unlock)
                if check_force_stop():
                    logger.info(f"⏹️ Người dùng đã dừng gửi hóa đơn tại {idx + 1}/{len(logs)} log (CHẾ ĐỘ 1)")
                    break

                if datetime.now().strftime("%Y-%m-%d") != today_str:
                    logger.info("⏹️ Đã sang ngày mới, dừng xuất hóa đơn còn lại.")
                    break

                try:
                    code = getattr(log, "transactionCode", "UNKNOWN")
                    fuel_code = getattr(log, "fuelType", "").lower()
                    fuel_name = FUEL_MAP.get(fuel_code, fuel_code)

                    # ✅ Gán thuế theo nhiên liệu từ cấu hình
                    fuel_code = getattr(log, "fuelType", "").upper()
                    xang_codes = ["A92", "A95_III", "A95_IV", "A95_V", "A97_IV", "A97_V", "E5"]

                    if setting.get("enableTaxFuel"):
                        is_xang = fuel_code in xang_codes
                        buyer_info["thue"] = setting.get("taxXang" if is_xang else "taxDau", "10")
                    else:
                        buyer_info["thue"] = setting.get("defaultCustomerTThue", "10")

                    
                    logger.info(f"📌 Gán thuế {buyer_info['thue']}% cho nhiên liệu: {fuel_code} ({fuel_name})")

                    tax_percent = buyer_info["thue"]
                    buyer_name = buyer_info.get("ten", "")

                    logger.info(f"📤 Đang xử lý: {code} @ {log.transactionTime} @ {fuel_name} @ Thuế {tax_percent}% @ {buyer_name}")

                    result = send_invoice_from_log(log, custom_data=buyer_info, from_auto=True)


                    if isinstance(result, dict) and result.get("success"):
                        shdon = result.get("shdon") or result.get("invoiceNumber") or "?"
                        logger.info(f"✅ {code} → HĐ: {shdon}")
                    else:
                        message = result.get("message", "Không rõ lỗi") if isinstance(result, dict) else str(result)
                        logger.info(f"❌ {code} bị từ chối: {message}")

                    log_sent_count += 1

                except Exception as e:
                    logger.exception(f"💥 Lỗi khi xử lý log: {getattr(log, 'transactionCode', '?')}")
                    continue

            logger.info(f"✅ Đã hoàn tất CHẾ ĐỘ 1. Đã xử lý {log_sent_count}/{log_total_count} log.")

        finally:
            unlock_invoice_send()
            isInvoiceProcessing = False

#==================== Mode_2 CHẾ ĐỘ 2 ===================================================================================
#   Yêu cầu xuất hóa đơn tự động (Chế độ 2):
#   X phút = chu kỳ chạy mỗi lần → ví dụ: 30 phút → sẽ chạy lúc: 00:30:00, 01:00:00, 01:30:00, ...
#   Y phút = khoảng thời gian cần chừa lại, để đảm bảo log đã ghi đầy đủ → ví dụ: Y = 5 phút
#   Khi đến mốc 01:30:00, bạn muốn:
#   → Chỉ xuất hóa đơn cho log < 01:25:00 (tức là 01:30:00 - 5 phút)
def run_mode_2(setting, buyer_info):
    now = datetime.now().replace(microsecond=0)
    today_str = now.strftime("%Y-%m-%d")

    if not setting.get("autoEveryXEnabled"):
        return

    everyXMin = int(setting.get("autoEveryXMin", 30))
    skipYMin = int(setting.get("autoSkipYMin", 30))
    maxAmount = int(setting.get("autoMaxAmount", 0))

    if now.minute % everyXMin != 0:
        return

    cutoff_time = now - timedelta(minutes=skipYMin)
    logger.info(f"⏳ Cắt mốc thời gian Y phút: {cutoff_time.strftime('%H:%M:%S')}")

    with app.app_context():
        db.session.remove()
        db.session.expire_all()
        logger.info("♻️ Đã gọi expire_all() để reload dữ liệu tươi từ DB")

        all_today_logs = db.session.query(Log).filter(Log.transactionDate == today_str).all()
        logger.info(f"🧪 Tổng log ngày {today_str}: {len(all_today_logs)}")

        logs1 = [l for l in all_today_logs if not l.isInvoiced or l.isInvoiced in [0, False, None]]
        logger.info(f"🧪 Sau lọc isInvoiced == False/0: {len(logs1)}")

        logs2 = [l for l in logs1 if not l.invoiceNumber or l.invoiceNumber in ["", "0", "null", None]]
        logger.info(f"🧪 Sau lọc invoiceNumber rỗng: {len(logs2)}")

        logs3 = []
        for log in logs2:
            try:
                log_time = datetime.strptime(f"{log.transactionDate} {log.transactionTime}", "%Y-%m-%d %H:%M:%S")
                if log_time >= cutoff_time:
                    continue
                if maxAmount > 0 and log.transactionCost >= maxAmount:
                    continue
                logs3.append(log)
            except Exception as e:
                logger.info(f"⚠️ Lỗi parse thời gian cho log {log.transactionCode}: {e}")
                continue

        logger.info(f"🧪 Sau lọc theo thời gian và số tiền: {len(logs3)}")

        logs = sorted(logs3, key=lambda l: (l.transactionDate, l.transactionTime))

        if not logs:
            logger.info(f"⚠️ Không có log nào phù hợp trong CHẾ ĐỘ 2 (ngày {today_str})")
            return

        for i, l in enumerate(logs[:2]):
            logger.info(f"🧾 Mẫu log {i+1}: {l.transactionCode} | HĐ={l.invoiceNumber} | Tiền={l.transactionCost} | isInv={l.isInvoiced}")

        if not lock_invoice_send():
            logger.info("⏹️ Bị chặn: không thể lock trước khi gửi (CHẾ ĐỘ 2).")
            return

        global isInvoiceProcessing, log_sent_count, log_total_count
        isInvoiceProcessing = True
        log_sent_count = 0
        log_total_count = len(logs)

        logger.info(f"▶ ĐANG CHẠY CHẾ ĐỘ 2... ({len(logs)} log)")

        try:
            for idx, log in enumerate(logs):
                # 🔁 Kiểm tra nếu người dùng đã bấm dừng (unlock)
                if check_force_stop():
                    logger.info(f"⏹️ Người dùng đã dừng gửi hóa đơn tại {idx + 1}/{len(logs)} log (CHẾ ĐỘ 2)")
                    break

                if datetime.now().strftime("%Y-%m-%d") != today_str:
                    logger.info("⏹️ Đã sang ngày mới, dừng xuất hóa đơn còn lại.")
                    break

                try:
                    code = getattr(log, "transactionCode", "UNKNOWN")
                    fuel_code = getattr(log, "fuelType", "").lower()
                    fuel_name = FUEL_MAP.get(fuel_code, fuel_code)

                    # ✅ Gán thuế theo nhiên liệu từ cấu hình
                    fuel_code = getattr(log, "fuelType", "").upper()
                    xang_codes = ["A92", "A95_III", "A95_IV", "A95_V", "A97_IV", "A97_V", "E5"]

                    if setting.get("enableTaxFuel"):
                        is_xang = fuel_code in xang_codes
                        buyer_info["thue"] = setting.get("taxXang" if is_xang else "taxDau", "10")
                    else:
                        buyer_info["thue"] = setting.get("defaultCustomerTThue", "10")

                    
                    logger.info(f"📌 Gán thuế {buyer_info['thue']}% cho nhiên liệu: {fuel_code} ({fuel_name})")

                    tax_percent = buyer_info["thue"]
                    buyer_name = buyer_info.get("ten", "")

                    logger.info(f"📤 Đang xử lý: {code} @ {log.transactionTime} @ {fuel_name} @ Thuế {tax_percent}% @ {buyer_name}")

                    result = send_invoice_from_log(log, custom_data=buyer_info, from_auto=True)


                    if isinstance(result, dict) and result.get("success"):
                        shdon = result.get("shdon") or result.get("invoiceNumber") or "?"
                        logger.info(f"✅ {code} → HĐ: {shdon}")
                    else:
                        message = result.get("message", "Không rõ lỗi") if isinstance(result, dict) else str(result)
                        logger.info(f"❌ {code} bị từ chối: {message}")

                    log_sent_count += 1

                except Exception as e:
                    logger.exception(f"💥 Lỗi khi xử lý log: {getattr(log, 'transactionCode', '?')}")
                    continue

            logger.info(f"✅ Đã hoàn tất CHẾ ĐỘ 2. Đã xử lý {log_sent_count}/{log_total_count} log.")

        finally:
            unlock_invoice_send()
            isInvoiceProcessing = False

#==================== Mode_3 CHẾ ĐỘ 3 ===================================================================================
#✅ Yêu cầu xuất hóa đơn tự động (Chế độ 3):
#   Người dùng chọn checkbox chọn log từ index, bấm nút Xuất HĐ
#   Thay thế cho hàm sendBatchInvoices_xxx
def run_mode_3(setting, buyer_info, codes):
    logger.info(f"▶ CHẾ ĐỘ 3: Gửi hóa đơn cho {len(codes)} giao dịch được chọn.")
    today_str = datetime.now().strftime("%Y-%m-%d")

    if not codes:
        logger.info("⚠️ Không có mã giao dịch nào được cung cấp.")
        return

    with app.app_context():
        db.session.remove()
        db.session.expire_all()

        try:
            logger.info("⏳ Bắt đầu truy vấn DB...")
            logs = db.session.query(Log).filter(Log.transactionCode.in_(codes)) \
                .order_by(Log.transactionDate, Log.transactionTime).all()
            logger.info(f"🧪 Các log chọn từ frontend: {len(logs)} log.")
        except Exception as e:
            logger.exception("💥 Lỗi truy vấn DB trong CHẾ ĐỘ 3")
            return

        if not logs:
            logger.info("⚠️ Không tìm thấy log phù hợp trong DB.")
            return

        global isInvoiceProcessing, log_sent_count, log_total_count
        isInvoiceProcessing = True
        log_sent_count = 0
        log_total_count = len(logs)

        if not lock_invoice_send():
            logger.info("⏹️ Bị chặn: không thể lock trước khi gửi (CHẾ ĐỘ 3).")
            return

        try:
            for idx, log in enumerate(logs):
                # 🔁 Kiểm tra nếu người dùng đã bấm dừng (unlock)
                if check_force_stop():
                    logger.info(f"⏹️ Người dùng đã dừng gửi hóa đơn tại {idx + 1}/{len(logs)} log (CHẾ ĐỘ 3)")
                    break

                if datetime.now().strftime("%Y-%m-%d") != today_str:
                    logger.info("⏹️ Đã sang ngày mới, dừng xuất hóa đơn còn lại.")
                    break

                try:
                    code = getattr(log, "transactionCode", "UNKNOWN")
                    fuel_code = getattr(log, "fuelType", "").lower()
                    fuel_name = FUEL_MAP.get(fuel_code, fuel_code)

                    # ✅ Gán thuế theo nhiên liệu từ cấu hình
                    fuel_code = getattr(log, "fuelType", "").upper()
                    xang_codes = ["A92", "A95_III", "A95_IV", "A95_V", "A97_IV", "A97_V", "E5"]

                    if setting.get("enableTaxFuel"):
                        is_xang = fuel_code in xang_codes
                        buyer_info["thue"] = setting.get("taxXang" if is_xang else "taxDau", "10")
                    else:
                        buyer_info["thue"] = setting.get("defaultCustomerTThue", "10")
                    
                    logger.info(f"📌 Gán thuế {buyer_info['thue']}% cho nhiên liệu: {fuel_code} ({fuel_name})")

                    tax_percent = buyer_info["thue"]
                    buyer_name = buyer_info.get("ten", "")

                    logger.info(f"📤 Đang xử lý: {code} @ {log.transactionTime} @ {fuel_name} @ Thuế {tax_percent}% @ {buyer_name}")

                    MAX_RETRY = 3
                    retry = 0

                    while retry < MAX_RETRY:
                        result = send_invoice_from_log(log, custom_data=buyer_info, from_auto=True)
                        db.session.refresh(log)  # cập nhật lại trạng thái mới nhất từ DB

                        if log.isInvoiced:
                            shdon = result.get("shdon") or result.get("invoiceNumber") or "?"
                            logger.info(f"✅ {code} → HĐ: {shdon} (sau lần {retry + 1})")
                            break
                        else:
                            retry += 1
                            message = result.get("message", "Không rõ lỗi") if isinstance(result, dict) else str(result)
                            logger.warning(f"❌ {code} chưa có HĐ sau lần {retry}: {message}")

                    if not log.isInvoiced:
                        logger.error(f"⛔ BỎ QUA: {code} sau {MAX_RETRY} lần thử – không xuất được hóa đơn.")

                    log_sent_count += 1

                except Exception as e:
                    logger.exception(f"💥 Lỗi khi xử lý log: {getattr(log, 'transactionCode', '?')}")
                    continue

            logger.info(f"✅ Đã hoàn tất CHẾ ĐỘ 3. Đã xử lý {log_sent_count}/{log_total_count} log.")

        finally:
            unlock_invoice_send()
            isInvoiceProcessing = False

#-----------------------------------------------------------------------------------------------------------------
import requests
def lock_invoice_send():
    try:
        # 🧠 Kiểm tra lock trước
        res = requests.get(f"http://127.0.0.1:{APP_PORT}/api/invoice-lock", timeout=3)
        if res.status_code != 200:
            raise Exception(f"Trạng thái HTTP không hợp lệ: {res.status_code}")

        try:
            is_locked = res.json().get("isProcessing", False)
        except Exception as je:
            raise Exception(f"Không đọc được JSON: {je} | nội dung: {res.text}")

        if is_locked:
            logger.info("⏸️ Bỏ qua: Đang có máy khác xuất hóa đơn")
            return False

        # ✅ Nếu không bị lock → tiến hành lock
        requests.post(f"http://127.0.0.1:{APP_PORT}/api/invoice-lock", json={"isProcessing": True}, timeout=3)
        logger.info("🔒 Đã bật LOCK gửi hóa đơn (backend)")
        return True

    except Exception as e:
        logger.info(f"⚠️ Không thể lock: {e}")
        return False

#-----------------------------------------------------------------------------------------------------------------
def unlock_invoice_send():
    try:
        requests.post(f"http://127.0.0.1:{APP_PORT}/api/invoice-lock", json={"isProcessing": False}, timeout=3)
        logger.info("🔓 Đã tắt LOCK gửi hóa đơn (backend)")
    except Exception as e:
        logger.info(f"⚠️ Không thể unlock: {e}")
        
#-----------------------------------------------------------------------------------------------------------------
def is_invoice_locked():
    try:
        res = requests.get(f"http://127.0.0.1:{APP_PORT}/api/invoice-lock", timeout=3)
        return res.json().get("isProcessing", False)
    except Exception as e:
        logger.info(f"⚠️ Không thể kiểm tra trạng thái lock: {e}")
        return False
#-----------------------------------------------------------------------------------------------------------------
def check_force_stop():
    try:
        res = requests.get(f"http://127.0.0.1:{APP_PORT}/api/invoice-lock", timeout=3)
        if res.status_code == 200:
            return res.json().get("isProcessing", False) == False
        return False
    except Exception as e:
        logger.info(f"⚠️ Không thể kiểm tra trạng thái dừng: {e}")
        return False
#-----------------------------------------------------------------------------------------------------------------
def main_loop():
    logger.info("🚀 Khởi động auto_scheduler.py")
    last_config_str = ""
    last_run_minute_mode2 = -1
    last_run_times_mode1 = set()

    while True:
        try:
            with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                config_str = f.read()

            if config_str != last_config_str:
                last_config_str = config_str
                setting = json.loads(config_str)
                buyer_info = build_default_buyer(setting)
                show_config_summary(setting, buyer_info)
            else:
                setting = json.loads(config_str)
                buyer_info = build_default_buyer(setting)

            # ✅ CHẾ ĐỘ 1: Chạy theo khung giờ cố định
            if setting.get("autoScheduleEnabled"):
                schedule_times = setting.get("autoScheduleTimes", [])
                now_str = datetime.now().strftime("%H:%M:%S")

                if now_str in schedule_times and now_str not in last_run_times_mode1:
                    import_logs_before_auto()
                    time.sleep(2)  # đảm bảo import xong rồi mới chạy
                    run_mode_1(setting, buyer_info)
                    last_run_times_mode1.add(now_str)

                # Giới hạn bộ nhớ set
                if len(last_run_times_mode1) > 100:
                    last_run_times_mode1.clear()

            # ✅ CHẾ ĐỘ 2: Chạy mỗi X phút, đúng 1 lần
            if setting.get("autoEveryXEnabled"):
                everyXMin = int(setting.get("autoEveryXMin", 30))
                current_minute = datetime.now().minute

                if current_minute % everyXMin == 0 and current_minute != last_run_minute_mode2:
                    import_logs_before_auto()
                    time.sleep(2)  # đảm bảo import xong rồi mới chạy                    
                    run_mode_2(setting, buyer_info)
                    last_run_minute_mode2 = current_minute

        except Exception as e:
            logger.info(f"💥 Lỗi hệ thống: {e}")

        time.sleep(1)


#-----------------------------------------------------------------------------------------------------------------
import threading
def auto_restart_on_change(py_path, settings_path, config_path):
    last_py_mtime = os.path.getmtime(py_path)
    last_settings_mtime = os.path.getmtime(settings_path)
    last_config_mtime = os.path.getmtime(config_path)

    while True:
        try:
            current_py_mtime = os.path.getmtime(py_path)
            current_settings_mtime = os.path.getmtime(settings_path)
            current_config_mtime = os.path.getmtime(config_path)

            if (
                current_py_mtime != last_py_mtime or
                current_settings_mtime != last_settings_mtime or
                current_config_mtime != last_config_mtime
            ):
                if isInvoiceProcessing:
                    logger.info("⚠️ Đang gửi hóa đơn → tạm hoãn restart để tránh lỗi.")
                    time.sleep(5)
                    continue

                logger.info("🔁 PHÁT HIỆN THAY ĐỔI (auto_scheduler.py hoặc settings/config.json) → ĐANG RESTART...")
                time.sleep(1)
                os.execv(sys.executable, ['python'] + sys.argv)

        except Exception as e:
            logger.info(f"⚠️ Lỗi theo dõi file: {e}")
        time.sleep(1)

#-----------------------------------------------------------------------------------------------------------------
if __name__ == "__main__":
    with app.app_context():
        config_json_path = os.path.join("config", "config.json")
        threading.Thread(
            target=auto_restart_on_change,
            args=(__file__, CONFIG_PATH, config_json_path),
            daemon=True
        ).start()
        main_loop()




