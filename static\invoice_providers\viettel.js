// ======== VIETTEL Invoice Provider =========================================



// Xem PDF hóa đơn từ log đã có
async function previewXML_VIETTEL(code) {
  console.log("📥 [VIETTEL] Gọi xem trước hóa đơn cho:", code);

  // Tìm log theo mã giao dịch
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (!log) return alert("Không tìm thấy giao dịch.");

  // Kiểm tra, nếu có số hóa đơn thì xử lý theo pattern BKAV/VNPT
  const hasInvoice = isValidInvoiceNumber(log.invoiceNumber);

  console.log("🟨 [VIETTEL] Kiểm tra invoiceNumber:", log.invoiceNumber);
  console.log("🟨 [VIETTEL] hasInvoice:", hasInvoice);
  console.log("🟨 [VIETTEL] log.provider:", log.provider);
  console.log("🟨 [VIETTEL] log.isInvoiced:", log.isInvoiced);

  if (hasInvoice) {
    console.log("📄 [VIETTEL] Hóa đơn đã tồn tại, hiển thị thông tin hóa đơn");

    // ✅ FIX: For existing invoices, follow VNPT pattern - show the standard preview modal
    // This provides consistent user experience across all providers
    // Don't try to fetch additional info, just show what we have in a standard format

    // Set up preview data similar to VNPT pattern
    window.previewData = {
      ...log,
      // Add some computed fields for display
      sl: log.transactionAmount || 0,
      dgChuaVat: log.transactionPrice || 0,
      ttVat: log.transactionCost || 0,
      ttChuaVat: (log.transactionCost || 0) * 0.9, // Approximate
      tienThue: (log.transactionCost || 0) * 0.1, // Approximate
      discountPerLit: 0,
      discountTotal: 0,
      congtienhang: log.transactionCost || 0,
      chu: "Đã xuất hóa đơn Viettel",
      thhdv: getMappedFuelName(log?.fuelType || ""),
      fuelType: log.fuelType,
      nl: log.transactionDate ? new Date(log.transactionDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
    };

    loadDefaultCustomerData();

    // Show the standard preview modal (consistent with VNPT)
    document.getElementById("xmlPreview")?.classList.remove("hidden");
    showModal("xmlPreview");
    updatePreview();

    // Add a note that this is an existing Viettel invoice
    setTimeout(() => {
      const previewTitle = document.querySelector("#xmlPreview .modal-title");
      if (previewTitle) {
        previewTitle.innerHTML = `🔵 Thông tin Hóa đơn Viettel - ${log.invoiceNumber}`;
      }
    }, 100);

    return;
  }

  // Nếu chưa có hóa đơn, gọi preview_invoice để lấy dữ liệu đầy đủ (consistent với BKAV/VNPT)
  console.log("📥 [VIETTEL] Gọi xem trước hóa đơn cho:", code);

  // Reset dữ liệu trước khi gọi lại (consistent với BKAV/VNPT)
  if (window.previewData) {
    window.previewData.discountPerLit = 0;
    window.previewData.discountTotal = 0;
    window.previewData.chu = "";
  }

  let thueStr = document.getElementById("formTThue")?.value || "10";
  let thue = 10;

  if (thueStr === "KCT" || thueStr === "KKKNT") {
    thue = 0;
  } else if (thueStr !== null && thueStr !== "") {
    thue = parseFloat(thueStr);
  }

  try {
    const res = await fetch('/preview_invoice', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        transactionCode: code,
        provider: 'viettel',
        customData: {
          ho_ten: document.getElementById("formHoTen")?.value || "",
          ten: document.getElementById("formTen")?.value || "",
          mst: document.getElementById("formMST")?.value || "",
          dchi: document.getElementById("formDChi")?.value || "",
          email: document.getElementById("formEmail")?.value || "",
          phone: document.getElementById("formPhone")?.value || "",
          plate: document.getElementById("formPlate")?.value || "",
          thue: thue,
          thueStr: thueStr,
          htttoan: document.getElementById("formHTTToan")?.value || "Tiền mặt/Chuyển khoản",
          mkhang: document.getElementById("formMKHang")?.value || "",
          thhdv: getMappedFuelName(log?.fuelType || ""),
          mdvqhns: document.getElementById("formMdvqhns")?.value || "",
          cccd: document.getElementById("formCCCD")?.value || ""
        }
      })
    });

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: ${res.statusText}`);
    }

  const result = await res.json();
  if (!result.success) {
    // Use SweetAlert2 for consistent error handling (aligned with BKAV/VNPT)
    console.error("❌ [VIETTEL] Preview error:", result);
    await Swal.fire({
      icon: "error",
      title: "❌ Lỗi xem trước",
      text: "Không thể xem trước hóa đơn Viettel: " + (result.message || "Lỗi không xác định"),
      timer: 8000,
      showConfirmButton: true
    });
    return;
  }

  // ✅ Gán dữ liệu đầy đủ từ backend vào previewData
  window.previewData = {
    ...log,
    nl: result.nlap || new Date().toISOString().split('T')[0], // Ngày lập từ backend hoặc ngày hiện tại
    sl: Number(result.sl),
    dgChuaVat: Number(result.dgChuaVat),
    ttVat: Number(result.ttVat),
    ttChuaVat: Number(result.ttChuaVat),
    tienThue: Number(result.tienThue),
    discountPerLit: Number(result.discountPerLit || 0),
    discountTotal: Number(result.discountTotal || 0),
    congtienhang: Number(result.congtienhang || result.ttVat),
    chu: result.chu || "",
    thhdv: result.thhdv || getMappedFuelName(log?.fuelType || ""),
    fuelType: result.fuelType || log.fuelType
  };

  loadDefaultCustomerData();

  // ✅ FIX: Use the standard modal like BKAV/VNPT instead of custom popup
  document.getElementById("xmlPreview")?.classList.remove("hidden");
  showModal("xmlPreview");
  updatePreview();

  } catch (error) {
    console.error("❌ [VIETTEL] Lỗi khi gọi preview API:", error);
    await Swal.fire({
      icon: "error",
      title: "❌ Lỗi kết nối",
      text: "Không thể kết nối đến server để xem trước hóa đơn. Vui lòng thử lại.",
      timer: 8000,
      showConfirmButton: true
    });
  }
}

// ✅ DEPRECATED: Custom popup function - now using standard modal like BKAV/VNPT
// This function is no longer used to ensure consistent UI experience
/*
function showPreviewPopup_VIETTEL(data, code) {
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (!log) return;

  const popup = document.createElement('div');
  popup.className = 'preview-popup';
  popup.innerHTML = `
    <div class="preview-content">
      <div class="preview-header">
        <h3>🔵 Preview Hóa Đơn Viettel</h3>
        <button class="close-btn" onclick="this.closest('.preview-popup').remove()">×</button>
      </div>
      
      <div class="preview-body">
        <div class="invoice-info">
          <h4>📋 Thông tin hóa đơn</h4>
          <div class="info-grid">
            <div><strong>Mã giao dịch:</strong> ${log.transactionCode}</div>
            <div><strong>Loại nhiên liệu:</strong> ${data.thhdv || log.fuelType}</div>
            <div><strong>Số lượng:</strong> ${data.sl || (log.transactionAmount/1000).toFixed(3)} lít</div>
            <div><strong>Đơn giá (chưa VAT):</strong> ${data.dgChuaVat || 0} VNĐ</div>
            <div><strong>Thành tiền (chưa VAT):</strong> ${(data.ttChuaVat || 0).toLocaleString()} VNĐ</div>
            <div><strong>Tiền thuế:</strong> ${(data.tienThue || 0).toLocaleString()} VNĐ</div>
            <div><strong>Tổng tiền:</strong> ${(data.ttVat || log.transactionCost).toLocaleString()} VNĐ</div>
            <div><strong>Bằng chữ:</strong> ${data.chu || ""}</div>
          </div>
        </div>

        <div class="customer-info">
          <h4>👤 Thông tin khách hàng</h4>
          <div class="form-grid">
            <div>
              <label>Họ tên:</label>
              <input type="text" id="previewHoTen" value="${document.getElementById("formHoTen")?.value || ""}" />
            </div>
            <div>
              <label>Tên riêng:</label>
              <input type="text" id="previewTen" value="${document.getElementById("formTen")?.value || ""}" />
            </div>
            <div>
              <label>Mã số thuế:</label>
              <input type="text" id="previewMST" value="${document.getElementById("formMST")?.value || ""}" />
            </div>
            <div>
              <label>Địa chỉ:</label>
              <input type="text" id="previewDChi" value="${document.getElementById("formDChi")?.value || ""}" />
            </div>
            <div>
              <label>Email:</label>
              <input type="text" id="previewEmail" value="${document.getElementById("formEmail")?.value || ""}" />
            </div>
            <div>
              <label>Số điện thoại:</label>
              <input type="text" id="previewPhone" value="${document.getElementById("formPhone")?.value || ""}" />
            </div>
            <div>
              <label>Biển số xe:</label>
              <input type="text" id="previewPlate" value="${document.getElementById("formPlate")?.value || ""}" />
            </div>
            <div>
              <label>Thuế suất (%):</label>
              <input type="text" id="previewTThue" value="${document.getElementById("formTThue")?.value || "10"}" />
            </div>
            <div>
              <label>Hình thức thanh toán:</label>
              <select id="previewHTTToan">
                <option value="Tiền mặt/Chuyển khoản" ${(document.getElementById("formHTTToan")?.value || "") === "Tiền mặt/Chuyển khoản" ? "selected" : ""}>Tiền mặt/Chuyển khoản</option>
                <option value="Tiền mặt" ${(document.getElementById("formHTTToan")?.value || "") === "Tiền mặt" ? "selected" : ""}>Tiền mặt</option>
                <option value="Chuyển khoản" ${(document.getElementById("formHTTToan")?.value || "") === "Chuyển khoản" ? "selected" : ""}>Chuyển khoản</option>
              </select>
            </div>
            <div>
              <label>Mã khách hàng:</label>
              <input type="text" id="previewMKHang" value="${document.getElementById("formMKHang")?.value || ""}" />
            </div>
          </div>
        </div>
      </div>

      <div class="preview-footer">
        <button class="btn-secondary" onclick="this.closest('.preview-popup').remove()">Đóng</button>
        <button class="btn-primary" onclick="sendInvoiceFromPopup_VIETTEL()">Xuất hóa đơn</button>
      </div>
    </div>
  `;

  document.body.appendChild(popup);
}
*/

// ======== Gửi từ popup Viettel (aligned with BKAV/VNPT patterns) =========================================
async function sendInvoiceFromPopup_VIETTEL() {
  const code = window.previewData?.transactionCode;
  if (!code) {
    await Swal.fire({
        icon: "error",
        title: " ❌ Không tìm thấy mã giao dịch.",
        timer: 8000,
        showConfirmButton: true
    });
    return;
  }

  // Nếu là log gộp (999999_xxx) thì gọi hàm riêng (consistent với BKAV)
  if (code.startsWith("999999_")) {
    return sendMergedInvoice(); // Đã định nghĩa trong main.js
  }

  // ✅ FIX: Check if this is an existing invoice (already has invoice number)
  // Follow VNPT pattern: Allow button to work but show appropriate message
  const log = currentFilteredLogs.find(l => l.transactionCode === code);
  if (log && isValidInvoiceNumber(log.invoiceNumber)) {
    await Swal.fire({
      icon: "info",
      title: "📄 Hóa đơn đã tồn tại",
      html: `
        <div style="text-align: left;">
          <p><strong>Số hóa đơn:</strong> ${log.invoiceNumber}</p>
          <p><strong>Mã giao dịch:</strong> ${code}</p>
          <p><strong>Nhà cung cấp:</strong> Viettel</p>
          <p><strong>Ngày xuất:</strong> ${log.transactionDate ? new Date(log.transactionDate).toLocaleDateString('vi-VN') : 'N/A'}</p>
          <p><strong>Số tiền:</strong> ${log.transactionCost ? new Intl.NumberFormat('vi-VN').format(log.transactionCost) + ' VND' : 'N/A'}</p>
          <hr>
          <p><em>Hóa đơn này đã được xuất thành công.</em></p>
          <p><em>Viettel chưa hỗ trợ xem PDF trực tiếp từ hệ thống.</em></p>
          <p><em>Bạn có thể đóng modal này.</em></p>
        </div>
      `,
      showConfirmButton: true,
      confirmButtonText: "Đóng"
    });
    return;
  }

  // ✅ FIX: Use standard modal form fields like BKAV/VNPT, not custom popup fields
  const modal = document.getElementById("xmlPreview");
  if (!modal) {
    console.error("❌ [VIETTEL] Modal #xmlPreview not found");
    return;
  }

  try {
    // ✅ FIX: Use correct form field IDs from standard modal (same as BKAV/VNPT)
    const customData = {
      ho_ten: document.getElementById("formHoTen")?.value || "",
      ten: document.getElementById("formTen")?.value || "",
      mst: document.getElementById("formMST")?.value || "",
      dchi: document.getElementById("formDChi")?.value || "",
      email: document.getElementById("formEmail")?.value || "",
      phone: document.getElementById("formPhone")?.value || "",
      plate: document.getElementById("formPlate")?.value || "",
      thue: parseFloat(document.getElementById("formTThue")?.value || "10"),
      thueStr: document.getElementById("formTThue")?.value || "10",
      htttoan: document.getElementById("formHTTToan")?.value || "Tiền mặt/Chuyển khoản",
      mkhang: document.getElementById("formMKHang")?.value || "",
      mdvqhns: document.getElementById("formMdvqhns")?.value || "",
      cccd: document.getElementById("formCCCD")?.value || "",
      thhdv: getMappedFuelName(window.previewData?.fuelType || "")
    };

    console.log("🔵 [VIETTEL] Gửi dữ liệu:", customData);

    const res = await fetch('/create_invoice', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionCode: code, customData })
    });

    const result = await res.json();
    console.log("📤 Viettel - Gửi hóa đơn từ popup:", result);

    let invoiceNo = result.invoiceNo || result.invoiceNumber || "";

    // Extract invoice number from response if needed (consistent với BKAV pattern)
    if (!invoiceNo) {
      try {
        const responseObj = result.response || {};

        if (Array.isArray(responseObj) && responseObj.length > 0) {
          invoiceNo =
            responseObj[0]?.InvoiceNo ||
            responseObj[0]?.invoiceNo;
        }

        if (!invoiceNo && typeof result.response === "string") {
          const match = result.response.match(/\b\d{4,11}\b/);
          invoiceNo = match ? match[0] : "";
        }
      } catch (e) {
        console.warn("❌ Lỗi parse response Viettel:", e);
      }
    }

    // Use SweetAlert2 for consistent user feedback (aligned with BKAV/VNPT)
    let msg = "";
    let icon = "success";

    if (result.success) {
      msg = invoiceNo ?
        `✅ Số hóa đơn là: <b>${invoiceNo}</b>` :
        "✅ Thành công (không rõ số hóa đơn)";

      // Cập nhật thông tin khách hàng vào form chính
      updateCustomerForm(customData);
    } else {
      let raw = result.response || result.message || "Không có phản hồi từ Viettel.";
      msg = `❌ Viettel trả lỗi:<br><pre style="text-align:left; white-space:pre-wrap;">${raw}</pre>`;
      icon = "error";
    }

    await Swal.fire({
      icon: icon,
      title: `Đã gửi: ${code}`,
      html: msg,
      timer: 8000,
      showConfirmButton: true
    });

    // ✅ FIX: Close modal and refresh logs AFTER SweetAlert (like VNPT pattern)
    closePreview();
    loadDefaultCustomerData();
    fetchLogs();

  } catch (error) {
    console.error("❌ Lỗi khi gửi hóa đơn Viettel:", error);
    await Swal.fire({
      icon: "error",
      title: "❌ Lỗi hệ thống",
      text: "Có lỗi xảy ra khi gửi hóa đơn. Vui lòng thử lại.",
      timer: 8000,
      showConfirmButton: true
    });
  } finally {
    await unlockInvoiceSend();
    window.isInvoiceProcessing = false;
    unlockScreen();
  }
}

// Legacy function for backward compatibility
async function submitInvoice_VIETTEL(code) {
  return await sendInvoiceFromPopup_VIETTEL();
}





// ======== Summary of fixes applied =========================================
/*
🔧 FIXES APPLIED TO VIETTEL INVOICE VIEWING:

1. ✅ MAIN ISSUE FIXED: Viettel now uses standard modal (#xmlPreview) like BKAV/VNPT
   - BEFORE: Used custom popup with class 'preview-popup'
   - AFTER: Uses same modal as other providers for consistent UI

2. ✅ UI CONSISTENCY:
   - BEFORE: Different popup layout and styling
   - AFTER: Same form layout, fields, and styling as BKAV/VNPT

3. ✅ BEHAVIOR ALIGNMENT:
   - Existing invoices: Show standard preview modal (like VNPT)
   - New invoices: Show standard preview modal for creation
   - Error handling: Consistent SweetAlert2 messages

4. ✅ CODE CLEANUP:
   - Deprecated showPreviewPopup_VIETTEL() function
   - Removed custom popup HTML generation
   - Aligned with BKAV/VNPT patterns

RESULT: Viettel invoice viewing now has identical UI/UX to BKAV and VNPT!

🔧 ADDITIONAL FIXES FOR BACKEND RESPONSE PROCESSING:

5. ✅ BACKEND DATABASE SAVE:
   - handle_viettel_submit now saves invoiceNumber to database like BKAV/VNPT
   - Removed duplicate database save in app.py
   - Consistent success response format

6. ✅ FRONTEND SEND BUTTON:
   - sendInvoiceFromPopup() now uses log-specific provider like previewLogByCode()
   - Fixed issue where Viettel invoices couldn't be sent from modal
   - Consistent provider detection across all functions

COMPLETE FLOW NOW WORKS:
- View Details → Correct modal → Send Invoice → Database updated → Success response
*/

// Hàm cập nhật form khách hàng
function updateCustomerForm(customData) {
  if (document.getElementById("formHoTen")) document.getElementById("formHoTen").value = customData.ho_ten || "";
  if (document.getElementById("formTen")) document.getElementById("formTen").value = customData.ten || "";
  if (document.getElementById("formMST")) document.getElementById("formMST").value = customData.mst || "";
  if (document.getElementById("formDChi")) document.getElementById("formDChi").value = customData.dchi || "";
  if (document.getElementById("formEmail")) document.getElementById("formEmail").value = customData.email || "";
  if (document.getElementById("formPhone")) document.getElementById("formPhone").value = customData.phone || "";
  if (document.getElementById("formPlate")) document.getElementById("formPlate").value = customData.plate || "";
  if (document.getElementById("formTThue")) document.getElementById("formTThue").value = customData.thue || "10";
  if (document.getElementById("formHTTToan")) document.getElementById("formHTTToan").value = customData.htttoan || "Tiền mặt/Chuyển khoản";
  if (document.getElementById("formMKHang")) document.getElementById("formMKHang").value = customData.mkhang || "";
}

console.log("✅ Viettel provider loaded successfully");
