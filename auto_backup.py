import os
import json
import time
import datetime
import threading

import logging
from logging.handlers import RotatingFileHandler
import os

os.makedirs("debug/logs_auto_backup", exist_ok=True)
logger = logging.getLogger("auto_backup")
logger.setLevel(logging.INFO)

if not logger.handlers:
    handler = RotatingFileHandler("debug/logs_auto_backup/auto_backup.log", maxBytes=1_000_000, backupCount=10, encoding="utf-8")
    formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)         
#-----------------------------------------------------------------------------------------------------------------
from app import app  # 📥 import app Flask

CONFIG_PATH = os.path.join("config", "settings.json")
MYSQLDUMP_PATH = r"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe"

# ✅ Phân tích thông tin từ cấu hình app
uri = app.config["SQLALCHEMY_DATABASE_URI"]
import re
match = re.match(r"mysql\+pymysql://(.*?):(.*?)@.*?/(.*?)(\?.*)?$", uri)
USER, PASSWORD, DB_NAME, _ = match.groups()

def run_backup_once():
    try:
        import subprocess

        logger.info("🚀 Bắt đầu tiến trình backup")

        with open(CONFIG_PATH, "r", encoding="utf-8") as f:
            conf = json.load(f)

        folder = conf.get("backupDir", "backup")
        filename = f"{DB_NAME}_backup.sql"

        # 🛡️ Kiểm tra quyền ghi thư mục
        try:
            os.makedirs(folder, exist_ok=True)
            test_path = os.path.join(folder, "~test.tmp")
            with open(test_path, "w") as tf:
                tf.write("test")
            os.remove(test_path)
            logger.info(f"📁 Sử dụng thư mục backupDir: {folder}")
        except Exception as e:
            logger.info(f"⚠️ Không thể ghi vào backupDir: {folder} — {e}")
            folder = "backup"
            os.makedirs(folder, exist_ok=True)
            logger.info(f"📂 Đã fallback về thư mục mặc định: {folder}")

        filepath = os.path.join(folder, filename)

        # ⚙️ Thực thi mysqldump bằng subprocess (ghi trực tiếp vào file)
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                result = subprocess.run(
                    [MYSQLDUMP_PATH, "--no-tablespaces", "-u", USER, f"-p{PASSWORD}", DB_NAME],
                    stdout=f,
                    stderr=subprocess.PIPE,
                    shell=False
                )
        except Exception as ex:
            logger.info(f"❌ Lỗi khi chạy mysqldump: {ex}")
            return f"Backup failed: {ex}"

        # ✅ Kiểm tra kết quả backup
        if result.returncode == 0 and os.path.exists(filepath) and os.path.getsize(filepath) > 0:
            logger.info(f"✅ Đã backup thành công vào: {filepath}")
        else:
            err_msg = result.stderr.decode(errors="ignore")
            logger.info(f"❌ Backup thất bại (mã {result.returncode}): {err_msg}")
            return f"Backup failed: {err_msg}"
            
        # -----------------------------------------------------------------------------------------------
        # 🧹 DỌN LOG CŨ HƠN 90 NGÀY
        logger.info("🧹 Bắt đầu dọn thư mục log cũ (debug/YYYYMMDD)")
        debug_root = "debug"
        keep_days = 90
        now = datetime.datetime.now()
        deleted = 0

        for folder_name in os.listdir(debug_root):
            folder_path = os.path.join(debug_root, folder_name)
            if not os.path.isdir(folder_path):
                continue
            try:
                folder_date = datetime.datetime.strptime(folder_name, "%Y%m%d")
                if folder_date < now - datetime.timedelta(days=keep_days):
                    import shutil
                    shutil.rmtree(folder_path)
                    logger.info(f"🗑️ Đã xóa log cũ: {folder_path}")
                    deleted += 1
            except:
                continue  # bỏ qua thư mục không đúng định dạng

        if deleted > 0:
            logger.info(f"🧹 Đã dọn {deleted} thư mục log cũ hơn {keep_days} ngày.")
        else:
            logger.info(f"🧹 Không có thư mục log nào cũ hơn {keep_days} ngày để xóa.")

        # -----------------------------------------------------------------------------------------------
        # XÓA CÁC LOG GHI DEBUG DẠNG app_stderr-*.log TRONG THƯ MỤC logs_std SAU 1 NGÀY
        # 🧹 Dọn file app_stderr-*.log không thuộc ngày hôm nay
        logger.info("🧹 Bắt đầu dọn file log app_stderr không thuộc ngày hôm nay (debug/logs_std)")
        debug_std_path = "debug/logs_std"
        today = datetime.datetime.now().date()
        deleted = 0

        for filename in os.listdir(debug_std_path):
            if not filename.startswith("app_stderr-") or not filename.endswith(".log"):
                continue

            filepath_stderr = os.path.join(debug_std_path, filename)
            try:
                mtime = datetime.datetime.fromtimestamp(os.path.getmtime(filepath_stderr))
                log_date = mtime.date()
                logger.info(f"📄 {filename} — mtime: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

                if log_date != today:
                    os.remove(filepath_stderr)
                    logger.info(f"🗑️ Đã xóa file: {filename}")
                    deleted += 1
                else:
                    logger.info("⏩ Giữ lại vì là log của hôm nay.")
            except Exception as e:
                logger.warning(f"⚠️ Không thể xử lý file {filename}: {e}")
                continue

        if deleted > 0:
            logger.info(f"🧹 Đã dọn {deleted} file log app_stderr không phải ngày hôm nay.")
        else:
            logger.info("🧹 Không có file log app_stderr nào cũ để xóa.")

        # -----------------------------------------------------------------------------------------------
    
    except Exception as e:
        logger.info(f"❌ Backup lỗi toàn cục: {e}")
        return str(e)


def schedule_auto_backup():
    def loop():
        logger.info("🟢 [AutoBackup] Đã khởi động tiến trình auto backup")
        while True:
            try:
                run_backup_once()
                with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                    delay = int(json.load(f).get("backupInterval", 60))
                logger.info(f"⏳ Đợi {delay} phút trước lần backup tiếp theo...")
                time.sleep(delay * 60)
            except Exception as e:
                logger.info(f"⚠️ Lỗi auto backup: {e}")
                time.sleep(60)
    threading.Thread(target=loop, daemon=True).start()
