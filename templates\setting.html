<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="utf-8" />
  <title>Xuất tự động</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
  <style>
    body {
      background: #f8f9fa;
      font-family: 'Arial', sans-serif;
    }

    .setting-container {
      max-width: 1024px;
      margin: 40px auto;
      background: white;
      padding: 30px 40px;
      border-radius: 16px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    label {
      font-weight: bold;
    }

    h3.title {
      color: #dc3545;
      font-weight: bold;
      text-align: center;
      margin-bottom: 32px;
    }
	
	button.btn {
	  width: 30%;
	  height: 52px;
	  font-size: 18px;
	  background-color: #063970;
	  color: white;
	  border: none;
	  border-radius: 8px;
	}
  </style>
<script>
  // ❌ Chặn chuột phải
  document.addEventListener('contextmenu', function (e) {
    e.preventDefault();
  });

  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
  document.addEventListener('keydown', function (e) {
    if (e.key === "F12") e.preventDefault();
    if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
      e.preventDefault();
    }
  });

</script>

</head>
<body>
  <div class="setting-container">
    <h3 class="title">CÀI ĐẶT</h3>

    <div class="row g-3">

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-md-12">
  <label style="color: #0d6efd;"><input type="checkbox" id="enableSchedule" onchange="toggleAutoFields()" /> Chế độ xuất tự động 1</label>
</div>

<div class="col-md-4">
  <label>Giờ 1:</label>
  <input type="time" id="scheduleTime1" class="form-control" />
  <input type="number" id="scheduleSecond1" class="form-control mt-1" placeholder="Giây" min="0" max="59" />
</div>
<div class="col-md-4">
  <label>Giờ 2:</label>
  <input type="time" id="scheduleTime2" class="form-control" />
  <input type="number" id="scheduleSecond2" class="form-control mt-1" placeholder="Giây" min="0" max="59" />
</div>
<div class="col-md-4">
  <label>Giờ 3:</label>
  <input type="time" id="scheduleTime3" class="form-control" />
  <input type="number" id="scheduleSecond3" class="form-control mt-1" placeholder="Giây" min="0" max="59" />
</div>

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-md-12">
  <label style="color: #0d6efd;"><input type="checkbox" id="enableAutoEveryX" onchange="toggleAutoFields()" /> Chế độ xuất tự động 2</label>
</div>

<div class="row mt-3">
  <div class="col-md-4">
    <label>Xuất hóa đơn mỗi (X phút):</label>
    <input type="number" id="autoEveryXMin" class="form-control" min="1" />
  </div>
  <div class="col-md-4">
    <label>Bỏ qua giao dịch (Y phút gần nhất):</label>
    <input type="number" id="skipLastYMin" class="form-control" min="1" />
  </div>
  <div class="col-md-4">
    <label>Bỏ qua số tiền (lớn hơn hoặc bằng):</label>
    <input type="number" id="maxAmountFilter" class="form-control" min="0" value="0" />
  </div>
</div>

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-md-12">
  <label style="color: #0d6efd;"><input type="checkbox" id="enableBuyer" onchange="toggleAutoFields()" /> [Ưu tiên] Thông tin xuất khách lẻ mặc định</label>
</div>

<div class="col-md-6">
  <label>Tên người mua:</label>
  <input id="defaultCustomerFullName" class="form-control" />
</div>
<div class="col-md-6">
  <label>Tên đơn vị mua:</label>
  <input id="defaultCustomerName" class="form-control" />
</div>
<div class="col-md-6">
  <label>Mã số thuế bên mua:</label>
  <input id="defaultCustomerTaxCode" class="form-control" />
</div>
<div class="col-md-6">
  <label>Địa chỉ bên mua:</label>
  <input id="defaultCustomerAddress" class="form-control" />
</div>

<div class="row">
  <div class="col-md-4">
    <label>Hình thức thanh toán:</label>
    <select id="defaultCustomerHTTToan" class="form-control">
      <option value="Tiền mặt/Chuyển khoản">Tiền mặt/Chuyển khoản</option>
      <option value="Tiền mặt">Tiền mặt</option>
      <option value="Chuyển khoản">Chuyển khoản</option>
    </select>
  </div>
  <div class="col-md-2">
    <label>Thuế suất (%):</label>
    <select id="defaultCustomerTThue" class="form-control">
      <option value="0">0%</option>
      <option value="5">5%</option>
      <option value="8">8%</option>
      <option value="10" selected>10%</option>
      <option value="KCT">KCT</option>
      <option value="KKKNT">KKKNT</option>
    </select>
  </div>
  <div class="col-md-6">
    <label>Mã khách hàng mua lẻ:</label>
    <input id="defaultCustomerMKHang" class="form-control" />
  </div>
</div>

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-md-12">
  <label style="color: #0d6efd;"><input type="checkbox" id="enableTaxFuel" onchange="toggleAutoFields()" /> [Chạy khi được tick] Thuế suất khác nhau cho xăng và dầu</label>
</div>

<div class="col-md-3">
  <label>Thuế suất Xăng (%)</label>
  <select class="form-select" id="taxXang">
      <option value="0">0%</option>
      <option value="5">5%</option>
      <option value="8" selected>8%</option>
      <option value="10">10%</option>
      <option value="KCT">KCT</option>
      <option value="KKKNT">KKKNT</option>
  </select>
</div>

<div class="col-md-3">
  <label>Thuế suất Dầu (%)</label>
  <select class="form-select" id="taxDau">
      <option value="0">0%</option>
      <option value="5">5%</option>
      <option value="8">8%</option>
      <option value="10" selected>10%</option>
      <option value="KCT">KCT</option>
      <option value="KKKNT">KKKNT</option>
  </select>
</div>

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-md-12">
  <label style="color: #0d6efd;"><input type="checkbox" id="enablefuelmap" onchange="toggleAutoFields()" /> Tên nhiên liệu xuất hóa đơn</label>
</div>
<hr class="my-1" style="border-top: 3px solid #ffffff;" />
<div class="row" id="fuelGrid"></div>

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-md-12">
  <label style="color: #0d6efd;"><input type="checkbox" id="enableTankMap" onchange="toggleAutoFields()" /> Gán serial vòi bơm với bồn nhiên liệu</label>
</div>
<hr class="my-1" style="border-top: 3px solid #ffffff;" />
<div class="row" id="tankGrid"></div>


<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="d-flex align-items-end gap-3">
  <div class="flex-grow-1">
    <label style="color: #0d6efd;">Thư mục lưu file backup:</label>
    <input id="backupDir" class="form-control" />
  </div>
  <div style="width: 160px;">
    <label>Mỗi X (phút):</label>
    <input id="backupInterval" type="number" class="form-control" />
  </div>
	<div style="min-width: 100px;">
	 <button onclick="triggerManualBackup()" class="btn w-100" style="background-color: #198754; color: white;">Xuất SQL</button>
	</div>
</div>

<hr class="my-3" style="border-top: 3px solid #0d6efd;" />

<div class="col-12 mt-3 text-center">
  <button class="btn btn-primary" onclick="saveSettings()">LƯU CÀI ĐẶT</button>
</div>


<script>
async function saveSettings() {
    const payload = {
      defaultCustomerFullName: document.getElementById("defaultCustomerFullName").value || "Người mua không lấy hóa đơn",
      defaultCustomerName: document.getElementById("defaultCustomerName").value || "",
      defaultCustomerTaxCode: document.getElementById("defaultCustomerTaxCode").value || "",
      defaultCustomerAddress: document.getElementById("defaultCustomerAddress").value || "",
      defaultCustomerHTTToan: document.getElementById("defaultCustomerHTTToan").value || "",
      defaultCustomerTThue: document.getElementById("defaultCustomerTThue").value,
      defaultCustomerMKHang: document.getElementById("defaultCustomerMKHang").value || "",
	  
	  enableTaxFuel: document.getElementById("enableTaxFuel").checked,
	  taxXang: document.getElementById("taxXang").value,
	  taxDau: document.getElementById("taxDau").value,

      autoScheduleEnabled: document.getElementById("enableSchedule").checked,
      autoEveryXEnabled: document.getElementById("enableAutoEveryX").checked,
      autoEveryXMin: Number(document.getElementById("autoEveryXMin").value || 30),
      autoSkipYMin: Number(document.getElementById("skipLastYMin").value || 30),
      autoMaxAmount: Number(document.getElementById("maxAmountFilter").value || 0),
      autoScheduleTimes: [],
	  backupDir: document.getElementById("backupDir").value || "backup",
	  backupInterval: Number(document.getElementById("backupInterval").value || 60)
    };

    for (let i = 1; i <= 3; i++) {
      const time = document.getElementById(`scheduleTime${i}`).value;
      const sec = document.getElementById(`scheduleSecond${i}`).value || "00";
      if (time) payload.autoScheduleTimes.push(`${time}:${sec.padStart(2, '0')}`);
    }

    const res = await fetch("/api/setting", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload)
    });

    const result = await res.json();
    if (result.success) {
      localStorage.setItem("settingsUpdatedAt", Date.now()); // Cho các tab khác biết
      alert("✅ Đã lưu cấu hình thành công.");
    } else {
      alert("❌ Lỗi khi lưu cấu hình.");
    }

    // ✅ Lưu nhiên liệu
    const fuelInputs = document.querySelectorAll("#fuelGrid input");
    const fuelData = {};
    fuelInputs.forEach(input => {
      fuelData[input.dataset.code] = input.value.trim();
    });

    try {
      const fuelRes = await fetch("/setting/fuel", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(fuelData)
      });
      const fuelResult = await fuelRes.json();
      if (!fuelResult.success) alert("❌ Không thể lưu fuel.json");
    } catch (e) {
      alert("❌ Lỗi khi lưu fuel.json: " + e.message);
    }
	
	await saveTankConfig();
}

function toggleAutoFields() {
  // Chế độ xuất tự động 2 (Every X minutes)
  const autoEnabled = document.getElementById("enableAutoEveryX").checked;
  document.getElementById("autoEveryXMin").disabled = !autoEnabled;
  document.getElementById("skipLastYMin").disabled = !autoEnabled;
  document.getElementById("maxAmountFilter").disabled = !autoEnabled;

  // Chế độ xuất tự động 1 (3 mốc thời gian cụ thể)
  const scheduleEnabled = document.getElementById("enableSchedule").checked;
  for (let i = 1; i <= 3; i++) {
    document.getElementById(`scheduleTime${i}`).disabled = !scheduleEnabled;
    document.getElementById(`scheduleSecond${i}`).disabled = !scheduleEnabled;
  }
  
  // Thông tin khách lẻ
  const buyerEnabled = document.getElementById("enableBuyer").checked;
  [
    "defaultCustomerFullName",
    "defaultCustomerName",
    "defaultCustomerTaxCode",
    "defaultCustomerAddress",
    "defaultCustomerHTTToan",
    "defaultCustomerTThue",
    "defaultCustomerMKHang"
  ].forEach(id => {
    document.getElementById(id).disabled = !buyerEnabled;
  });

	// Thuế xăng/dầu riêng biệt
	const taxSplitEnabled = document.getElementById("enableTaxFuel").checked;
	document.getElementById("taxXang").disabled = !taxSplitEnabled;
	document.getElementById("taxDau").disabled = !taxSplitEnabled;


  // Map tên nhiên liệu
  const fuelMapEnabled = document.getElementById("enablefuelmap").checked;
  const fuelInputs = document.querySelectorAll("#fuelGrid input");
  fuelInputs.forEach(input => {
    input.disabled = !fuelMapEnabled;
  });
  // Map bồn nhiên liệu
  const tankMapEnabled = document.getElementById("enableTankMap").checked;
  const tankInputs = document.querySelectorAll("#tankGrid input");
  tankInputs.forEach(input => {
  input.disabled = !tankMapEnabled;
  });
}


window.onload = async () => {
    const res = await fetch("/api/setting");
    const data = await res.json();

    document.getElementById("defaultCustomerFullName").value = data.defaultCustomerFullName || "";
    document.getElementById("defaultCustomerName").value = data.defaultCustomerName || "";
    document.getElementById("defaultCustomerTaxCode").value = data.defaultCustomerTaxCode || "";
    document.getElementById("defaultCustomerAddress").value = data.defaultCustomerAddress || "";
    document.getElementById("defaultCustomerHTTToan").value = data.defaultCustomerHTTToan || "";
    document.getElementById("defaultCustomerTThue").value = data.defaultCustomerTThue || "10";
    document.getElementById("defaultCustomerMKHang").value = data.defaultCustomerMKHang || "";

    document.getElementById("enableSchedule").checked = data.autoScheduleEnabled === true;
    document.getElementById("enableAutoEveryX").checked = data.autoEveryXEnabled === true;
    document.getElementById("autoEveryXMin").value = data.autoEveryXMin || 30;
    document.getElementById("skipLastYMin").value = data.autoSkipYMin || 30;
    document.getElementById("maxAmountFilter").value = data.autoMaxAmount || 0;
	
	document.getElementById("backupDir").value = data.backupDir || "";
    document.getElementById("backupInterval").value = data.backupInterval || 60;

	document.getElementById("enableTaxFuel").checked = data.enableTaxFuel === true;
	document.getElementById("taxXang").value = data.taxXang || "8";
	document.getElementById("taxDau").value = data.taxDau || "10";


    const times = data.autoScheduleTimes || [];
    for (let i = 1; i <= 3; i++) {
      const fullTime = times[i - 1] || "";
      const [hh = "", mm = "", ss = ""] = fullTime.split(":");
      document.getElementById(`scheduleTime${i}`).value = hh && mm ? `${hh}:${mm}` : "";
      document.getElementById(`scheduleSecond${i}`).value = ss || "";
    }

	await loadFuelTable();
	await loadTankTable();
	toggleAutoFields();      
};

// --------------- Map Fuel ----------------------------------------------------------------------------
async function loadFuelTable() {
    const res = await fetch("/setting/fuel");
    const data = await res.json();
    const grid = document.getElementById("fuelGrid");
    grid.innerHTML = "";

    Object.entries(data).forEach(([code, name]) => {
      const col = document.createElement("div");
      col.className = "col-md-3 mb-3";
      col.innerHTML = `
        <label class="form-label">${code}</label>
        <input class="form-control" value="${name}" data-code="${code}" />
      `;
      grid.appendChild(col);
    });
}

async function saveFuelConfig() {
	const inputs = document.querySelectorAll("#fuelTable input[data-code]");
	const data = {};

	inputs.forEach(input => {
	  data[input.dataset.code] = input.value.trim();
	});

	const res = await fetch("/setting/fuel", {
	  method: "POST",
	  headers: { "Content-Type": "application/json" },
	  body: JSON.stringify(data)
	});

	const result = await res.json();
	if (result.success) {
	  alert("✅ Đã lưu fuel.json thành công");
	} else {
	  alert("❌ Không thể lưu fuel.json");
	}
}

// --------------- Map Tank ----------------------------------------------------------------------------
async function loadTankTable() {
    const res = await fetch("/api/tank_map");
    const result = await res.json();
    const grid = document.getElementById("tankGrid");
    grid.innerHTML = "";

    if (!result.success) {
        grid.innerHTML = "<p class='text-danger'>Không thể tải danh sách bồn nhiên liệu.</p>";
        return;
    }

    result.data.forEach(item => {
        const col = document.createElement("div");
        col.className = "col-md-3 mb-3";
        col.innerHTML = `
            <label class="form-label">${item.pumpSerial}</label>
            <input class="form-control" value="${item.tank}" data-hose="${item.pumpSerial}" />
        `;
        grid.appendChild(col);
    });
}



async function saveTankConfig() {
    const inputs = document.querySelectorAll("#tankGrid input[data-hose]");
    const data = {};
    inputs.forEach(input => {
        data[input.dataset.hose] = input.value.trim();
    });

    try {
        const res = await fetch("/setting/tank", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data)
        });

        const result = await res.json();
        if (!result.success) alert("❌ Không thể lưu tank.json");
    } catch (e) {
        alert("❌ Lỗi khi lưu tank.json: " + e.message);
    }
}


// --------------- Backup SQL ----------------------------------------------------------------------------

async function triggerManualBackup() {
	const res = await fetch("/api/manual-backup");
	const data = await res.json();
	if (data.success) {
	alert("✅ Đã backup database thành công!");
	} else {
	alert("❌ Backup thất bại: " + (data.message || "Không rõ lỗi."));
	}
}


</script>
<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>
</body>
</html>
