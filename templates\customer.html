<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Qu<PERSON><PERSON> lý k<PERSON> hàng</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"/>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
  <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <style>
	.modal {
	position: fixed;
	top: 0; left: 0;
	width: 100vw; height: 100vh;
	background-color: rgba(0,0,0,0.5);
	align-items: center;
	justify-content: center;
	display: none; /* mặc định ẩn */
	}

	.modal.show {
	display: flex !important; /* khi có class .show thì hiển thị */
	}

	.close {
	float: right;
	font-size: 24px;
	cursor: pointer;
	}
	.modal-content {
	background-color: white;
	padding: 20px;
	border-radius: 10px;
	width: 800px;
	max-height: 90vh;
	overflow-y: auto;
	box-shadow: 0 5px 15px rgba(0,0,0,0.3);
	}
	th {
	  background-color: #003366 !important;  /* giữ nền xanh đậm */
	  color: white;
	  text-align: center;
	  vertical-align: middle !important;
	  white-space: normal !important; /* cho phép xuống dòng */
	  padding: 8px 6px;
	  font-size: 13px;
	} 
    .header-tools {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 16px;
      align-items: center;
    }

    .swal2-html-container {
      text-align: left !important;
    }
  </style>
<script>
  // ❌ Chặn chuột phải
	document.addEventListener('contextmenu', function (e) {
		const tag = e.target.tagName.toLowerCase();
		const isEditable = ['input', 'textarea'].includes(tag) || e.target.isContentEditable;

		if (!isEditable) {
			e.preventDefault(); // chỉ chặn ngoài vùng nhập
		}
	});


  // ❌ Chặn phím tắt DevTools: F12, Ctrl+Shift+I/J/C, Ctrl+U
	document.addEventListener('keydown', function (e) {
		if (e.key === "F12") e.preventDefault();
		if ((e.ctrlKey && e.shiftKey && ["I", "J", "C"].includes(e.key)) || (e.ctrlKey && e.key === "U")) {
			e.preventDefault();
		}
	});

</script>

</head>
<body class="d-flex flex-column min-vh-100">
  <div class="container-fluid px-3 flex-grow-1">
	<div class="header-row d-flex align-items-center justify-content-between flex-wrap">
	  <!-- Logo và tên công ty -->
	  <div class="d-flex align-items-center gap-2">
		<img src="/static/favicon.ico" alt="Logo" style="height: 40px; border-radius: 8px; cursor: pointer;" onclick="location.reload()" />
		<div>
		  <h2 id="sellerName" class="m-0" style="color:  #dc3545; font-weight: bold; font-size: 20px;">CÔNG TY TNHH KỸ THUẬT MON</h2>
			<p class="m-0" style="font-size: 12px; font-weight: bold; color: #063970; white-space: nowrap;">
			  <span id="sellerTaxcode">**********</span>
			  <span id="sellerFullName">Montech</span>
			  <span id="providerLabel" class="text-muted ms-1" style="font-weight: normal;"></span>
			</p>
		</div>
	  </div>

	  <!-- Phần bên phải: Cập nhật + Cài đặt + User -->
	  <div class="d-flex align-items-center gap-3">
		<!-- ✅ Trạng thái cập nhật -->
		<div id="fetchStatus" style="font-size: 14px; color: gray; text-align: center;"></div>
		<div id="activeUserCount" style="font-size: 14px; color: gray; text-align: center;"></div>		
	  </div>
	  
	</div>
    <div style="height: 1px; background-color: #ccc; margin: 6px 0;"></div>

  <div class="header-tools">
    <input type="text" id="searchInput" class="form-control" placeholder="Tìm kiếm khách hàng đã có hoặc nhập MST tìm online ..." style="width: 450px !important; height: 38px !important; font-size: 14px !important;" oninput="searchCustomer()">
	<input type="file" id="fileInput" class="d-none" accept=".xlsx,.xls" onchange="handleImportExcel(event)">
    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">Nhập File KH</button>
    <button class="btn btn-success" onclick="exportCustomers()">Xuất File KH</button>
  </div>
    <div class="table-wrapper">
      <table id="logTable" class="table table-bordered table-hover">
		<thead>
        <tr>
          <th>STT</th>
		  <th class="sortable" onclick="sortCustomerTable(1)">Mã KH <i class="fa-solid fa-sort"></i></th>
		  <th class="sortable" onclick="sortCustomerTable(2)">Người mua <i class="fa-solid fa-sort"></i></th>
          <th>Tên đơn vị</th>
          <th>Địa chỉ</th>
          <th>MST</th>
          <th>Email</th>
          <th>Điện thoại</th>
          <th>Biển số</th>
          <th>Mã ĐVQHNS</th>
          <th>Hành động</th>
        </tr>
      </thead>
      <tbody id="customerTable"></tbody>
    </table>
  </div>
</div>

<footer class="text-center text-muted py-2 mt-auto" style="font-size: 14px;">
  © Montech - 2025
</footer>

<script>
//----------------------------------------------------------------------------
fetch("/load_config_invoice")
  .then(res => res.json())
  .then(config => {
    window.currentConfig = config;

    const provider = config.provider?.toLowerCase() || "vnpt";

    // Cập nhật dòng providerLabel
    const labelEl = document.getElementById("providerLabel");
    if (labelEl) labelEl.textContent = `(${provider.toUpperCase()})`;

    // Cập nhật trạng thái auto gửi
    //updateAutoConfigStatus();

    // Cập nhật tên công ty + MST
    // Cập nhật tên công ty + MST
    const conf = config[provider.toUpperCase()] || {};
    const nameEl = document.getElementById("sellerName");
    const taxEl = document.getElementById("sellerTaxcode");
    const FullNameEl = document.getElementById("sellerFullName");	

    if (nameEl) nameEl.textContent = conf.sellerName || "";
    if (taxEl) taxEl.textContent = `MST: ${conf.sellerTaxCode || ""} - `;
	if (FullNameEl) FullNameEl.textContent = conf.sellerFullName || "";
  })
  .catch(err => {
    console.error("❌ Lỗi load config:", err);
  });
  
//----------------------------------------------------------------------------
let currentLogs = [];
function loadResetLogs() {
  updateFetchStatus("🟢 Đang tải GD...");

  fetch('/logs')
    .then(res => res.json())
    .then(data => {
      currentLogs = data.logs;

      // ✅ Hiển thị trạng thái cập nhật thành công
      const now = new Date().toLocaleTimeString('vi-VN', { hour12: false });
      updateFetchStatus(`✅ Cập nhật ${now}`);
    })
    .catch(err => {
      console.error("❌ Lỗi tải logs:", err);
      updateFetchStatus("❌ Không thể tải logs");
    });
}
// Nút trạng thái đang cập nhật log
function updateFetchStatus(status) {
  const el = document.getElementById("fetchStatus");
  if (!el) return;
  el.innerText = status;
}
// === Xử lý đếm số lượng người truy cập ======================
async function showActiveUsers() {
  try {
    const res = await fetch('/active_users');
    const data = await res.json();
    document.getElementById('activeUserCount').innerText = `🟢 Online: ${data.active}`;
  } catch (e) {
    console.warn("Không thể lấy số người dùng hoạt động.");
  }
}  
  
let customers = [];

function normalize(str) {
  return str.normalize("NFD").replace(/\p{Diacritic}/gu, '').toLowerCase();
}

function renderTable() {
  const tbody = document.getElementById('customerTable');
  tbody.innerHTML = '';
  customers.forEach((c, idx) => {
    tbody.insertAdjacentHTML('beforeend', `
      <tr>
        <td>${idx + 1}</td>
		<td contenteditable="false">${c.mkhang || ''}</td>
        <td contenteditable="false">${c.buyer}</td>
        <td contenteditable="false">${c.company}</td>
        <td contenteditable="false">${c.address}</td>
        <td contenteditable="false">${c.tax}</td>
        <td contenteditable="false">${c.email}</td>
        <td contenteditable="false">${c.phone}</td>
        <td contenteditable="false">${c.plate}</td>
        <td contenteditable="false">${c.mdvqhns}</td>		
		<td>
		<div class="d-flex justify-content-center gap-1">
		  <button class="btn btn-sm" style="background-color: #ffc107 !important; color: black;" onclick="editCustomer(${idx}, this)">Sửa</button>
		  <button class="btn btn-sm" style="background-color: #dc3545 !important; color: white;" onclick="confirmDelete(${idx})">Xóa</button>
		</div>

		</td>
      </tr>`);
  });
}

function editCustomer(i, btn) {
  const row = document.getElementById('customerTable').rows[i];
  const cells = row.querySelectorAll('td');

  // Bật chỉnh sửa từ cột 1 đến 8
  for (let j = 1; j <= 9; j++) {
    cells[j].setAttribute('contenteditable', 'true');
  }

  // ✅ Focus vào ô "Mã KH" (ô thứ 1 )
  cells[1].focus();

  // Cập nhật nút
// Cập nhật nút
	btn.innerText = 'Lưu';
	btn.className = 'btn btn-sm';
	btn.style.backgroundColor = '#0d6efd';  // Màu xanh Bootstrap
	btn.style.color = 'white';
	btn.onclick = () => saveCustomer(i);
}


function saveCustomer(i) {
  const row = document.getElementById('customerTable').rows[i].cells;
  const customer = {
    id: customers[i].id, // lấy đúng ID backend trả về
    mkhang: row[1].innerText.trim(),
    buyer: row[2].innerText.trim(),
    company: row[3].innerText.trim(),
    address: row[4].innerText.trim(),
    tax: row[5].innerText.trim(),
    email: row[6].innerText.trim(),
    phone: row[7].innerText.trim(),
    plate: row[8].innerText.trim(),
    mdvqhns: row[9].innerText.trim()	
  };
  customers[i] = customer;
  fetch("/api/customers", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(customer)
  }).then(() => {
	Swal.fire({
	  icon: "success",
	  title: "Đã lưu thay đổi",
	  timer: 1500,
	  showConfirmButton: false
	});
    renderTable();
  });
}


function confirmDelete(i) {
  Swal.fire({
    title: 'Xác nhận xoá khách hàng?', icon: 'warning', showCancelButton: true,
    confirmButtonText: 'Xoá', cancelButtonText: 'Hủy'
  }).then(result => {
    if (result.isConfirmed) {
      const idToDelete = customers[i].id;
      fetch(`/api/customers/${idToDelete}`, { method: "DELETE" })
        .then(res => res.json())
        .then(() => {
          customers.splice(i, 1);
          renderTable();
          	Swal.fire({
			  icon: "success",
			  title: "Đã xóa khách hàng",
			  timer: 1500,
			  showConfirmButton: false
			});
        });
    }
  });
}

async function searchCustomer() {
  const inputEl = document.getElementById('searchInput');
  const rawValue = inputEl.value.trim();
  const input = rawValue.toLowerCase();

  // Normalize để lọc bảng
  const keyword = normalize(rawValue);

  // Lọc bảng KH theo keyword
  document.querySelectorAll('#customerTable tr').forEach(row => {
    const text = normalize(row.innerText);
    row.style.display = text.includes(keyword) ? '' : 'none';
  });

  // Nếu trống hoặc không phải MST thì dừng
  if (!rawValue || !/^[0-9]{10,13}(-\d{3})?$/.test(rawValue)) return;

  // Kiểm tra KH đã có trong local
  const found = customers.find(c =>
    (c.mkhang || "").toLowerCase().includes(input) ||
    (c.company || "").toLowerCase().includes(input) ||
    (c.tax || "").toLowerCase().includes(input)
  );

  if (found) return; // Đã có trong danh sách → dừng

  // Không tìm thấy → tìm online
  const info = await fetchMSTVietQR(rawValue);
  if (!info) return;

  const mkhang = "KHONGMA";
  const taxCode = info.taxCode || rawValue;

  // Kiểm tra trùng KHONGMA+MST
  const trung = customers.find(c => c.mkhang === mkhang && c.tax === taxCode);
  if (trung) return;

  const newCus = {
    mkhang: mkhang,
    buyer: '',
    company: info.name || '',
    address: info.address || '',
    tax: taxCode,
    email: '',
    phone: '',
    plate: '',
    mdvqhns: ''
  };

  // Hiển thị popup xác nhận
	const confirm = await Swal.fire({
	  icon: 'success',
	  title: 'ĐÃ TÌM THẤY KHÁCH HÀNG',
	  html: `<div style='text-align: left'>
		<b>${info.name || ''}</b><br>
		MST: ${taxCode}<br>
		${info.address || ''}
	  </div>`,
	  showCancelButton: true,
	  confirmButtonText: 'Thêm vào hệ thống',
	  cancelButtonText: 'Hủy'
	});


  if (!confirm.isConfirmed) return;

  // Gửi lên server
  const res = await fetch("/api/customers", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(newCus)
  });
  const result = await res.json();

  if (result.success && result.id) {
    const newList = await fetch("/api/customers").then(res => res.json());
    customers = newList.map(c => {
      delete c._sa_instance_state;
      return c;
    });
    renderTable();
	Swal.fire({
	  icon: "success",
	  title: "Đã lưu khách hàng",
	  timer: 1500,
	  showConfirmButton: false
	}).then(() => {
	  location.reload(); // F5 sau khi popup đóng
	});
  } else {
    Swal.fire('Không thể lưu khách hàng');
  }
}


async function fetchMSTVietQR(mst) {
  try {
    const res = await fetch(`https://api.vietqr.io/v2/business/${mst}`);
    const json = await res.json();
    if (json.code === '00') return json.data;
    throw new Error(json.desc);
  } catch (err) {
    Swal.fire('❌ Không tìm thấy MST', err.message, 'error');
    return null;
  }
}




function exportCustomers() {
  const ws = XLSX.utils.json_to_sheet(customers.map(c => ({
    "Mã KH": c.mkhang,
    "Người mua": c.buyer,
    "Tên đơn vị": c.company,
    "Địa chỉ": c.address,
    "MST": c.tax,
    "Email": c.email,
    "Điện thoại": c.phone,
    "Biển số": c.plate,
    "Mã ĐVQHNS": c.mdvqhns
  })));
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "KhachHang");
  XLSX.writeFile(wb, "KH_MontechPOS.xlsx");
	Swal.fire({
	  icon: "success",
	  title: "Đã xuất file",
	  timer: 1500,
	  showConfirmButton: false
	});
}

function handleImportExcel(e) {
  const file = e.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = function(evt) {
    const data = new Uint8Array(evt.target.result);
    const workbook = XLSX.read(data, { type: 'array' });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = XLSX.utils.sheet_to_json(sheet);

    rows.forEach(row => {
      const mkhang = (row["Mã KH"]?.trim() || "KHONGMA");

      const updated = {
        mkhang: mkhang,
        buyer: row["Người mua"] || '',
        company: row["Tên đơn vị"] || '',
        address: row["Địa chỉ"] || '',
        tax: row["MST"] || '',
        email: row["Email"] || '',
        phone: row["Điện thoại"] || '',
        plate: row["Biển số"] || '',
        mdvqhns: row["Mã ĐVQHNS"] || ''		
      };

      // ✅ Chỉ kiểm tra trùng nếu mã KH khác "KM"
      const index = customers.findIndex(c => c.mkhang === mkhang && mkhang !== "KHONGMA");

      if (index !== -1) {
        customers[index] = { ...customers[index], ...updated };
      } else {
        customers.push(updated);
      }

      // ✅ Gửi từng khách hàng lên server để lưu vào DB
      fetch("/api/customers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updated)
      });
    });

	Swal.fire({
	  icon: "success",
	  title: "Đã nhập và lưu khách hàng",
	  timer: 1200,
	  showConfirmButton: false
	}).then(() => {
	  location.reload(); // ✅ F5 sau khi popup đóng
	});
  };

  reader.readAsArrayBuffer(file);
}

// Sort mã KH và tên người mua
let currentSortCol = null;
let currentSortDir = 'asc';

function sortCustomerTable(colIndex) {
  const getValue = (c, col) => {
    if (col === 1) return c.mkhang || "";
    if (col === 2) return c.buyer || "";
    return "";
  };

  if (currentSortCol === colIndex) {
    currentSortDir = (currentSortDir === 'asc') ? 'desc' : 'asc';
  } else {
    currentSortCol = colIndex;
    currentSortDir = 'asc';
  }

  customers.sort((a, b) => {
    const valA = getValue(a, colIndex).toLowerCase();
    const valB = getValue(b, colIndex).toLowerCase();
    if (valA < valB) return currentSortDir === 'asc' ? -1 : 1;
    if (valA > valB) return currentSortDir === 'asc' ? 1 : -1;
    return 0;
  });

  renderTable();
}


// ✅ Ngăn lỗi khi paste vào các ô contenteditable
document.addEventListener('paste', function(e) {
  const active = document.activeElement;
  if (active && active.getAttribute('contenteditable') === 'true') {
    e.preventDefault();
    const text = (e.clipboardData || window.clipboardData).getData('text/plain');
    document.execCommand('insertText', false, text);
  }
});

window.addEventListener("DOMContentLoaded", () => {
  fetch("/api/customers")
    .then(res => res.json())
    .then(data => {
      customers = data.map(c => {
        delete c._sa_instance_state;
        return c;
      });
      renderTable();
    });
});

window.onload = async function () {
	showActiveUsers();
	loadResetLogs();
};
</script>

<style>
  .swal2-html-container { text-align: left !important; }
  td[contenteditable="true"]:focus { background: #fff8dc; }

  /* Ngăn tất cả cột rớt dòng */
  #customerTable td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Chỉ cho phép 2 cột này rớt dòng */
  #customerTable td:nth-child(4),
  #customerTable td:nth-child(5) {
    white-space: normal;
    word-break: break-word;
  }
  
  
    /* Không cho rớt dòng trong toàn bộ tiêu đề bảng */
  thead th {
    white-space: nowrap;
  }
</style>

</body>
</html>
